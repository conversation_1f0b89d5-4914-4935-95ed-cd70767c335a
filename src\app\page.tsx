'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Download, Loader2, Sparkles } from 'lucide-react';
import { Hero, Skin, FrameConfig } from '@/types/aov';
import { frameTypes, masteryLevels, spells, ranks } from '@/lib/frame-data';
import { downloadImage } from '@/lib/utils';
import { Instructions } from '@/components/Instructions';
import { Footer } from '@/components/Footer';

export default function HomePage() {
  const [config, setConfig] = useState<FrameConfig>({
    frameType: null,
    hero: null,
    skin: null,
    playerName: '',
    companion: null,
    masteryLevel: null,
    spell: null,
    rank: null,
    rankNumber: undefined,
  });

  const [heroes, setHeroes] = useState<Hero[]>([]);
  const [skins, setSkins] = useState<Skin[]>([]);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);

  // Fetch heroes on component mount
  useEffect(() => {
    const fetchHeroes = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/heroes');
        const data = await response.json();
        setHeroes(data);
      } catch (error) {
        console.error('Error fetching heroes:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchHeroes();
  }, []);

  // Fetch skins when hero changes
  useEffect(() => {
    const fetchSkins = async () => {
      if (!config.hero) {
        setSkins([]);
        return;
      }

      setLoading(true);
      try {
        const response = await fetch(`/api/skins/${config.hero.slug}`);
        const data = await response.json();
        setSkins(data);
      } catch (error) {
        console.error('Error fetching skins:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSkins();
  }, [config.hero]);

  const handleGenerateImage = async () => {
    if (!config.frameType || !config.hero || !config.skin || !config.playerName.trim()) {
      alert('Vui lòng điền đầy đủ thông tin: Loại khung, Tướng, Skin và Tên người chơi');
      return;
    }

    setGenerating(true);
    try {
      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error('Failed to generate image');
      }

      const blob = await response.blob();
      const imageUrl = URL.createObjectURL(blob);
      setGeneratedImage(imageUrl);
    } catch (error) {
      console.error('Error generating image:', error);
      alert('Có lỗi xảy ra khi tạo ảnh. Vui lòng thử lại.');
    } finally {
      setGenerating(false);
    }
  };

  const handleDownload = () => {
    if (generatedImage) {
      downloadImage(generatedImage, `aov-frame-${config.playerName || 'player'}.png`);
    }
  };

  const oldFramesWithBorder = frameTypes.filter(f => f.category === 'old' && f.hasBorder);
  const oldFramesWithoutBorder = frameTypes.filter(f => f.category === 'old' && !f.hasBorder);
  const newFrames = frameTypes.filter(f => f.category === 'new');

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4 flex items-center justify-center gap-2">
            <Sparkles className="h-8 w-8 text-yellow-400" />
            AOV Frame Generator
            <Sparkles className="h-8 w-8 text-yellow-400" />
          </h1>
          <p className="text-lg text-gray-300">
            Tạo khung hình ảnh Liên Quân Mobile một cách dễ dàng và miễn phí
          </p>
        </div>

        {/* Instructions */}
        <Instructions />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Configuration Panel */}
          <div className="lg:col-span-2 space-y-6">
            {/* Frame Type Selection */}
            <Card className="aov-card">
              <CardHeader>
                <CardTitle className="text-white">Chọn loại khung</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Khung cũ - Có viền vàng
                  </label>
                  <Select
                    value={config.frameType?.id || ''}
                    onValueChange={(value) => {
                      const frameType = frameTypes.find(f => f.id === value);
                      setConfig(prev => ({ ...prev, frameType: frameType || null }));
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn khung có viền" />
                    </SelectTrigger>
                    <SelectContent>
                      {oldFramesWithBorder.map((frame) => (
                        <SelectItem key={frame.id} value={frame.id}>
                          {frame.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Khung cũ - Không viền vàng
                  </label>
                  <Select
                    value={config.frameType?.id || ''}
                    onValueChange={(value) => {
                      const frameType = frameTypes.find(f => f.id === value);
                      setConfig(prev => ({ ...prev, frameType: frameType || null }));
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn khung không viền" />
                    </SelectTrigger>
                    <SelectContent>
                      {oldFramesWithoutBorder.map((frame) => (
                        <SelectItem key={frame.id} value={frame.id}>
                          {frame.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Khung mới
                  </label>
                  <Select
                    value={config.frameType?.id || ''}
                    onValueChange={(value) => {
                      const frameType = frameTypes.find(f => f.id === value);
                      setConfig(prev => ({ ...prev, frameType: frameType || null }));
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn khung mới" />
                    </SelectTrigger>
                    <SelectContent>
                      {newFrames.map((frame) => (
                        <SelectItem key={frame.id} value={frame.id}>
                          {frame.name} {frame.hasBorder ? '(Có viền)' : '(Không viền)'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Hero and Skin Selection */}
            <Card className="aov-card">
              <CardHeader>
                <CardTitle className="text-white">Chọn tướng và skin</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Chọn tướng
                  </label>
                  <Select
                    value={config.hero?.id || ''}
                    onValueChange={(value) => {
                      const hero = heroes.find(h => h.id === value);
                      setConfig(prev => ({ ...prev, hero: hero || null, skin: null }));
                    }}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={loading ? "Đang tải..." : "Chọn tướng"} />
                    </SelectTrigger>
                    <SelectContent>
                      {heroes.map((hero) => (
                        <SelectItem key={hero.id} value={hero.id}>
                          {hero.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Chọn skin
                  </label>
                  <Select
                    value={config.skin?.id || ''}
                    onValueChange={(value) => {
                      const skin = skins.find(s => s.id === value);
                      setConfig(prev => ({ ...prev, skin: skin || null }));
                    }}
                    disabled={!config.hero || loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={
                        !config.hero ? "Chọn tướng trước" :
                        loading ? "Đang tải..." :
                        "Chọn skin"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {skins.map((skin) => (
                        <SelectItem key={skin.id} value={skin.id}>
                          {skin.name} {skin.rarity && `(${skin.rarity})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Additional Options */}
            <Card className="aov-card">
              <CardHeader>
                <CardTitle className="text-white">Tùy chọn bổ sung</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Thông thạo / Cục top
                  </label>
                  <Select
                    value={config.masteryLevel?.id || 'none'}
                    onValueChange={(value) => {
                      const mastery = masteryLevels.find(m => m.id === value);
                      setConfig(prev => ({ ...prev, masteryLevel: mastery || null }));
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn thông thạo" />
                    </SelectTrigger>
                    <SelectContent>
                      {masteryLevels.map((mastery) => (
                        <SelectItem key={mastery.id} value={mastery.id}>
                          {mastery.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Phép bổ trợ
                  </label>
                  <Select
                    value={config.spell?.id || 'none'}
                    onValueChange={(value) => {
                      const spell = spells.find(s => s.id === value);
                      setConfig(prev => ({ ...prev, spell: spell || null }));
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn phép bổ trợ" />
                    </SelectTrigger>
                    <SelectContent>
                      {spells.map((spell) => (
                        <SelectItem key={spell.id} value={spell.id}>
                          {spell.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Thứ hạng
                  </label>
                  <Select
                    value={config.rank?.id || 'none'}
                    onValueChange={(value) => {
                      const rank = ranks.find(r => r.id === value);
                      setConfig(prev => ({ ...prev, rank: rank || null }));
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn thứ hạng" />
                    </SelectTrigger>
                    <SelectContent>
                      {ranks.map((rank) => (
                        <SelectItem key={rank.id} value={rank.id}>
                          {rank.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {config.rank && config.rank.id !== 'none' && (
                  <div>
                    <label className="text-sm font-medium text-gray-300 mb-2 block">
                      Số thứ hạng (tùy chọn)
                    </label>
                    <Input
                      type="number"
                      value={config.rankNumber || ''}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        rankNumber: e.target.value ? parseInt(e.target.value) : undefined
                      }))}
                      placeholder="Nhập số thứ hạng"
                      className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Player Name */}
            <Card className="aov-card">
              <CardHeader>
                <CardTitle className="text-white">Thông tin người chơi</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Tên người chơi
                  </label>
                  <Input
                    value={config.playerName}
                    onChange={(e) => setConfig(prev => ({ ...prev, playerName: e.target.value }))}
                    placeholder="Nhập tên của bạn"
                    className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Preview and Generate */}
          <div className="space-y-6">
            <Card className="aov-card">
              <CardHeader>
                <CardTitle className="text-white">Xem trước</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-square bg-gray-800 rounded-lg flex items-center justify-center mb-4">
                  {generatedImage ? (
                    <img
                      src={generatedImage}
                      alt="Generated frame"
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <div className="text-center text-gray-400">
                      <Sparkles className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>Ảnh sẽ hiển thị ở đây</p>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Button
                    onClick={handleGenerateImage}
                    disabled={generating || !config.frameType || !config.hero || !config.skin || !config.playerName.trim()}
                    className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                  >
                    {generating ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Đang tạo ảnh...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Tạo ảnh
                      </>
                    )}
                  </Button>

                  {generatedImage && (
                    <Button
                      onClick={handleDownload}
                      variant="outline"
                      className="w-full border-white/20 text-white hover:bg-white/10"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Tải xuống
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}
