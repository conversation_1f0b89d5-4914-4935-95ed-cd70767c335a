"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-image/route";
exports.ids = ["app/api/generate-image/route"];
exports.modules = {

/***/ "canvas":
/*!*************************!*\
  !*** external "canvas" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("canvas");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_Website_AOV_data_main_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-image/route.ts */ \"(rsc)/./src/app/api/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-image/route\",\n        pathname: \"/api/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-image/route\"\n    },\n    resolvedPagePath: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\api\\\\generate-image\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_Website_AOV_data_main_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-image/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-image/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/generate-image/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_image_generator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/image-generator */ \"(rsc)/./src/lib/image-generator.ts\");\n\n\nasync function POST(request) {\n    try {\n        const config = await request.json();\n        // Validate required fields\n        if (!config.frameType || !config.hero || !config.skin || !config.playerName?.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        const generator = new _lib_image_generator__WEBPACK_IMPORTED_MODULE_1__.ImageGenerator();\n        const imageBuffer = await generator.generateFrame(config);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(imageBuffer, {\n            headers: {\n                \"Content-Type\": \"image/png\",\n                \"Content-Length\": imageBuffer.length.toString()\n            }\n        });\n    } catch (error) {\n        console.error(\"Error in generate-image API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to generate image\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-image/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/image-generator.ts":
/*!************************************!*\
  !*** ./src/lib/image-generator.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageGenerator: () => (/* binding */ ImageGenerator)\n/* harmony export */ });\n/* harmony import */ var canvas__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! canvas */ \"canvas\");\n/* harmony import */ var canvas__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(canvas__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nclass ImageGenerator {\n    constructor(){\n        this.width = 800;\n        this.height = 800;\n        this.canvas = (0,canvas__WEBPACK_IMPORTED_MODULE_0__.createCanvas)(this.width, this.height);\n        this.ctx = this.canvas.getContext(\"2d\");\n    }\n    async generateFrame(config) {\n        // Clear canvas\n        this.ctx.fillStyle = \"#000000\";\n        this.ctx.fillRect(0, 0, this.width, this.height);\n        try {\n            // Load and draw skin image\n            if (config.skin?.image) {\n                await this.drawSkinImage(config.skin.image);\n            }\n            // Load and draw frame\n            if (config.frameType) {\n                await this.drawFrame(config.frameType.path);\n            }\n            // Draw player name\n            if (config.playerName) {\n                this.drawPlayerName(config.playerName);\n            }\n            // Draw mastery level\n            if (config.masteryLevel && config.masteryLevel.id !== \"none\") {\n                await this.drawMasteryLevel(config.masteryLevel.image);\n            }\n            // Draw spell\n            if (config.spell && config.spell.id !== \"none\") {\n                await this.drawSpell(config.spell.image);\n            }\n            // Draw rank\n            if (config.rank && config.rank.id !== \"none\") {\n                await this.drawRank(config.rank.image, config.rankNumber);\n            }\n            return this.canvas.toBuffer(\"image/png\");\n        } catch (error) {\n            console.error(\"Error generating frame:\", error);\n            throw new Error(\"Failed to generate frame\");\n        }\n    }\n    async drawSkinImage(imageUrl) {\n        try {\n            // Add headers to avoid CORS issues\n            const image = await (0,canvas__WEBPACK_IMPORTED_MODULE_0__.loadImage)(imageUrl);\n            // Strategy: Fill the entire canvas with the skin image (crop to fit)\n            // This ensures the skin image covers the full frame area\n            const imageAspectRatio = image.width / image.height;\n            const canvasAspectRatio = this.width / this.height;\n            let drawWidth, drawHeight, x, y;\n            if (imageAspectRatio > canvasAspectRatio) {\n                // Image is wider than canvas - fit by height and crop sides\n                drawHeight = this.height;\n                drawWidth = this.height * imageAspectRatio;\n                x = (this.width - drawWidth) / 2;\n                y = 0;\n            } else {\n                // Image is taller than canvas - fit by width and crop top/bottom\n                drawWidth = this.width;\n                drawHeight = this.width / imageAspectRatio;\n                x = 0;\n                y = (this.height - drawHeight) / 2;\n            }\n            // Draw the skin image to fill the entire canvas\n            this.ctx.drawImage(image, x, y, drawWidth, drawHeight);\n        } catch (error) {\n            console.error(\"Error loading skin image:\", error);\n            // Draw a more attractive placeholder\n            this.drawPlaceholder(\"Skin Image\");\n        }\n    }\n    drawPlaceholder(text) {\n        // Gradient background\n        const gradient = this.ctx.createLinearGradient(0, 0, this.width, this.height);\n        gradient.addColorStop(0, \"#667eea\");\n        gradient.addColorStop(1, \"#764ba2\");\n        this.ctx.fillStyle = gradient;\n        this.ctx.fillRect(0, 0, this.width, this.height);\n        // Border\n        this.ctx.strokeStyle = \"#ffffff\";\n        this.ctx.lineWidth = 4;\n        this.ctx.strokeRect(2, 2, this.width - 4, this.height - 4);\n        // Text\n        this.ctx.fillStyle = \"#ffffff\";\n        this.ctx.font = \"bold 32px Arial\";\n        this.ctx.textAlign = \"center\";\n        this.ctx.textBaseline = \"middle\";\n        this.ctx.fillText(text, this.width / 2, this.height / 2);\n    }\n    async drawFrame(framePath) {\n        try {\n            // Try to load frame from local assets\n            const frameImagePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"public\", framePath, \"frame.png\");\n            if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(frameImagePath)) {\n                const frameImage = await (0,canvas__WEBPACK_IMPORTED_MODULE_0__.loadImage)(frameImagePath);\n                this.ctx.drawImage(frameImage, 0, 0, this.width, this.height);\n            } else {\n                // Draw a simple frame border if no frame image exists\n                this.drawSimpleFrame();\n            }\n        } catch (error) {\n            console.error(\"Error loading frame:\", error);\n            this.drawSimpleFrame();\n        }\n    }\n    drawSimpleFrame() {\n        // Draw a more sophisticated frame that doesn't obscure the skin image\n        const borderWidth = 15;\n        // Create gradient for frame\n        const gradient = this.ctx.createLinearGradient(0, 0, this.width, this.height);\n        gradient.addColorStop(0, \"#FFD700\");\n        gradient.addColorStop(0.5, \"#FFA500\");\n        gradient.addColorStop(1, \"#FF8C00\");\n        // Draw frame border\n        this.ctx.strokeStyle = gradient;\n        this.ctx.lineWidth = borderWidth;\n        this.ctx.strokeRect(borderWidth / 2, borderWidth / 2, this.width - borderWidth, this.height - borderWidth);\n        // Add inner glow effect\n        this.ctx.shadowColor = \"#FFD700\";\n        this.ctx.shadowBlur = 10;\n        this.ctx.strokeStyle = \"#FFFFFF\";\n        this.ctx.lineWidth = 2;\n        this.ctx.strokeRect(borderWidth + 2, borderWidth + 2, this.width - (borderWidth + 2) * 2, this.height - (borderWidth + 2) * 2);\n        // Reset shadow\n        this.ctx.shadowColor = \"transparent\";\n        this.ctx.shadowBlur = 0;\n    }\n    drawPlayerName(name) {\n        // Draw a more attractive name banner\n        const nameY = this.height - 70;\n        const nameHeight = 40;\n        const padding = 20;\n        // Create gradient background for name\n        const gradient = this.ctx.createLinearGradient(0, nameY, 0, nameY + nameHeight);\n        gradient.addColorStop(0, \"rgba(0, 0, 0, 0.8)\");\n        gradient.addColorStop(1, \"rgba(0, 0, 0, 0.6)\");\n        this.ctx.fillStyle = gradient;\n        this.ctx.fillRect(padding, nameY, this.width - padding * 2, nameHeight);\n        // Add border to name banner\n        this.ctx.strokeStyle = \"#FFD700\";\n        this.ctx.lineWidth = 2;\n        this.ctx.strokeRect(padding, nameY, this.width - padding * 2, nameHeight);\n        // Draw name text with shadow effect\n        this.ctx.shadowColor = \"rgba(0, 0, 0, 0.8)\";\n        this.ctx.shadowOffsetX = 2;\n        this.ctx.shadowOffsetY = 2;\n        this.ctx.shadowBlur = 4;\n        this.ctx.fillStyle = \"#FFFFFF\";\n        this.ctx.font = \"bold 24px Arial\";\n        this.ctx.textAlign = \"center\";\n        this.ctx.textBaseline = \"middle\";\n        this.ctx.fillText(name, this.width / 2, nameY + nameHeight / 2);\n        // Reset shadow\n        this.ctx.shadowColor = \"transparent\";\n        this.ctx.shadowOffsetX = 0;\n        this.ctx.shadowOffsetY = 0;\n        this.ctx.shadowBlur = 0;\n    }\n    async drawMasteryLevel(imagePath) {\n        try {\n            const masteryImage = await (0,canvas__WEBPACK_IMPORTED_MODULE_0__.loadImage)(imagePath);\n            const size = 60;\n            const x = this.width - size - 20;\n            const y = 20;\n            this.ctx.drawImage(masteryImage, x, y, size, size);\n        } catch (error) {\n            console.error(\"Error loading mastery image:\", error);\n        }\n    }\n    async drawSpell(imagePath) {\n        try {\n            const spellImage = await (0,canvas__WEBPACK_IMPORTED_MODULE_0__.loadImage)(imagePath);\n            const size = 50;\n            const x = 20;\n            const y = this.height - size - 20;\n            this.ctx.drawImage(spellImage, x, y, size, size);\n        } catch (error) {\n            console.error(\"Error loading spell image:\", error);\n        }\n    }\n    async drawRank(imagePath, rankNumber) {\n        try {\n            const rankImage = await (0,canvas__WEBPACK_IMPORTED_MODULE_0__.loadImage)(imagePath);\n            const size = 60;\n            const x = 20;\n            const y = 20;\n            this.ctx.drawImage(rankImage, x, y, size, size);\n            // Draw rank number if provided\n            if (rankNumber) {\n                this.ctx.fillStyle = \"#FFFFFF\";\n                this.ctx.font = \"bold 16px Arial\";\n                this.ctx.textAlign = \"center\";\n                this.ctx.fillText(rankNumber.toString(), x + size / 2, y + size + 20);\n            }\n        } catch (error) {\n            console.error(\"Error loading rank image:\", error);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/image-generator.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();