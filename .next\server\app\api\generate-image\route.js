"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-image/route";
exports.ids = ["app/api/generate-image/route"];
exports.modules = {

/***/ "canvas":
/*!*************************!*\
  !*** external "canvas" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("canvas");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_Website_AOV_data_main_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-image/route.ts */ \"(rsc)/./src/app/api/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-image/route\",\n        pathname: \"/api/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-image/route\"\n    },\n    resolvedPagePath: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\api\\\\generate-image\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_Website_AOV_data_main_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-image/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-image/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/generate-image/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_image_generator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/image-generator */ \"(rsc)/./src/lib/image-generator.ts\");\n\n\nasync function POST(request) {\n    try {\n        const config = await request.json();\n        // Validate required fields\n        if (!config.frameType || !config.hero || !config.skin || !config.playerName?.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        const generator = new _lib_image_generator__WEBPACK_IMPORTED_MODULE_1__.ImageGenerator();\n        const imageBuffer = await generator.generateFrame(config);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(imageBuffer, {\n            headers: {\n                \"Content-Type\": \"image/png\",\n                \"Content-Length\": imageBuffer.length.toString()\n            }\n        });\n    } catch (error) {\n        console.error(\"Error in generate-image API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to generate image\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZW5lcmF0ZS1pbWFnZS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0Q7QUFDRDtBQUdoRCxlQUFlRSxLQUFLQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTUMsU0FBc0IsTUFBTUQsUUFBUUUsSUFBSTtRQUU5QywyQkFBMkI7UUFDM0IsSUFBSSxDQUFDRCxPQUFPRSxTQUFTLElBQUksQ0FBQ0YsT0FBT0csSUFBSSxJQUFJLENBQUNILE9BQU9JLElBQUksSUFBSSxDQUFDSixPQUFPSyxVQUFVLEVBQUVDLFFBQVE7WUFDbkYsT0FBT1YscURBQVlBLENBQUNLLElBQUksQ0FDdEI7Z0JBQUVNLE9BQU87WUFBMEIsR0FDbkM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU1DLFlBQVksSUFBSVosZ0VBQWNBO1FBQ3BDLE1BQU1hLGNBQWMsTUFBTUQsVUFBVUUsYUFBYSxDQUFDWDtRQUVsRCxPQUFPLElBQUlKLHFEQUFZQSxDQUFDYyxhQUFhO1lBQ25DRSxTQUFTO2dCQUNQLGdCQUFnQjtnQkFDaEIsa0JBQWtCRixZQUFZRyxNQUFNLENBQUNDLFFBQVE7WUFDL0M7UUFDRjtJQUNGLEVBQUUsT0FBT1AsT0FBTztRQUNkUSxRQUFRUixLQUFLLENBQUMsZ0NBQWdDQTtRQUM5QyxPQUFPWCxxREFBWUEsQ0FBQ0ssSUFBSSxDQUN0QjtZQUFFTSxPQUFPO1FBQTJCLEdBQ3BDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW92LWZyYW1lLWdlbmVyYXRvci8uL3NyYy9hcHAvYXBpL2dlbmVyYXRlLWltYWdlL3JvdXRlLnRzP2NiMWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IEltYWdlR2VuZXJhdG9yIH0gZnJvbSAnQC9saWIvaW1hZ2UtZ2VuZXJhdG9yJztcbmltcG9ydCB7IEZyYW1lQ29uZmlnIH0gZnJvbSAnQC90eXBlcy9hb3YnO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IGNvbmZpZzogRnJhbWVDb25maWcgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICBcbiAgICAvLyBWYWxpZGF0ZSByZXF1aXJlZCBmaWVsZHNcbiAgICBpZiAoIWNvbmZpZy5mcmFtZVR5cGUgfHwgIWNvbmZpZy5oZXJvIHx8ICFjb25maWcuc2tpbiB8fCAhY29uZmlnLnBsYXllck5hbWU/LnRyaW0oKSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnTWlzc2luZyByZXF1aXJlZCBmaWVsZHMnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCBnZW5lcmF0b3IgPSBuZXcgSW1hZ2VHZW5lcmF0b3IoKTtcbiAgICBjb25zdCBpbWFnZUJ1ZmZlciA9IGF3YWl0IGdlbmVyYXRvci5nZW5lcmF0ZUZyYW1lKGNvbmZpZyk7XG4gICAgXG4gICAgcmV0dXJuIG5ldyBOZXh0UmVzcG9uc2UoaW1hZ2VCdWZmZXIsIHtcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdpbWFnZS9wbmcnLFxuICAgICAgICAnQ29udGVudC1MZW5ndGgnOiBpbWFnZUJ1ZmZlci5sZW5ndGgudG9TdHJpbmcoKSxcbiAgICAgIH0sXG4gICAgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gZ2VuZXJhdGUtaW1hZ2UgQVBJOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGdlbmVyYXRlIGltYWdlJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsIkltYWdlR2VuZXJhdG9yIiwiUE9TVCIsInJlcXVlc3QiLCJjb25maWciLCJqc29uIiwiZnJhbWVUeXBlIiwiaGVybyIsInNraW4iLCJwbGF5ZXJOYW1lIiwidHJpbSIsImVycm9yIiwic3RhdHVzIiwiZ2VuZXJhdG9yIiwiaW1hZ2VCdWZmZXIiLCJnZW5lcmF0ZUZyYW1lIiwiaGVhZGVycyIsImxlbmd0aCIsInRvU3RyaW5nIiwiY29uc29sZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-image/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/image-generator.ts":
/*!************************************!*\
  !*** ./src/lib/image-generator.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageGenerator: () => (/* binding */ ImageGenerator)\n/* harmony export */ });\n/* harmony import */ var canvas__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! canvas */ \"canvas\");\n/* harmony import */ var canvas__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(canvas__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nclass ImageGenerator {\n    constructor(){\n        this.width = 800;\n        this.height = 800;\n        this.canvas = (0,canvas__WEBPACK_IMPORTED_MODULE_0__.createCanvas)(this.width, this.height);\n        this.ctx = this.canvas.getContext(\"2d\");\n    }\n    async generateFrame(config) {\n        // Clear canvas\n        this.ctx.fillStyle = \"#000000\";\n        this.ctx.fillRect(0, 0, this.width, this.height);\n        try {\n            // Load and draw skin image\n            if (config.skin?.image) {\n                await this.drawSkinImage(config.skin.image);\n            }\n            // Load and draw frame\n            if (config.frameType) {\n                await this.drawFrame(config.frameType.path);\n            }\n            // Draw player name\n            if (config.playerName) {\n                this.drawPlayerName(config.playerName);\n            }\n            // Draw mastery level\n            if (config.masteryLevel && config.masteryLevel.id !== \"none\") {\n                await this.drawMasteryLevel(config.masteryLevel.image);\n            }\n            // Draw spell\n            if (config.spell && config.spell.id !== \"none\") {\n                await this.drawSpell(config.spell.image);\n            }\n            // Draw rank\n            if (config.rank && config.rank.id !== \"none\") {\n                await this.drawRank(config.rank.image, config.rankNumber);\n            }\n            return this.canvas.toBuffer(\"image/png\");\n        } catch (error) {\n            console.error(\"Error generating frame:\", error);\n            throw new Error(\"Failed to generate frame\");\n        }\n    }\n    async drawSkinImage(imageUrl) {\n        try {\n            // Add headers to avoid CORS issues\n            const image = await (0,canvas__WEBPACK_IMPORTED_MODULE_0__.loadImage)(imageUrl);\n            // Calculate dimensions to fit the canvas while maintaining aspect ratio\n            const aspectRatio = image.width / image.height;\n            let drawWidth = this.width;\n            let drawHeight = this.height;\n            if (aspectRatio > 1) {\n                drawHeight = this.width / aspectRatio;\n            } else {\n                drawWidth = this.height * aspectRatio;\n            }\n            const x = (this.width - drawWidth) / 2;\n            const y = (this.height - drawHeight) / 2;\n            this.ctx.drawImage(image, x, y, drawWidth, drawHeight);\n        } catch (error) {\n            console.error(\"Error loading skin image:\", error);\n            // Draw a more attractive placeholder\n            this.drawPlaceholder(\"Skin Image\");\n        }\n    }\n    drawPlaceholder(text) {\n        // Gradient background\n        const gradient = this.ctx.createLinearGradient(0, 0, this.width, this.height);\n        gradient.addColorStop(0, \"#667eea\");\n        gradient.addColorStop(1, \"#764ba2\");\n        this.ctx.fillStyle = gradient;\n        this.ctx.fillRect(0, 0, this.width, this.height);\n        // Border\n        this.ctx.strokeStyle = \"#ffffff\";\n        this.ctx.lineWidth = 4;\n        this.ctx.strokeRect(2, 2, this.width - 4, this.height - 4);\n        // Text\n        this.ctx.fillStyle = \"#ffffff\";\n        this.ctx.font = \"bold 32px Arial\";\n        this.ctx.textAlign = \"center\";\n        this.ctx.textBaseline = \"middle\";\n        this.ctx.fillText(text, this.width / 2, this.height / 2);\n    }\n    async drawFrame(framePath) {\n        try {\n            // Try to load frame from local assets\n            const frameImagePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"public\", framePath, \"frame.png\");\n            if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(frameImagePath)) {\n                const frameImage = await (0,canvas__WEBPACK_IMPORTED_MODULE_0__.loadImage)(frameImagePath);\n                this.ctx.drawImage(frameImage, 0, 0, this.width, this.height);\n            } else {\n                // Draw a simple frame border if no frame image exists\n                this.drawSimpleFrame();\n            }\n        } catch (error) {\n            console.error(\"Error loading frame:\", error);\n            this.drawSimpleFrame();\n        }\n    }\n    drawSimpleFrame() {\n        // Draw a simple golden frame\n        const borderWidth = 20;\n        // Outer border\n        this.ctx.strokeStyle = \"#FFD700\";\n        this.ctx.lineWidth = borderWidth;\n        this.ctx.strokeRect(borderWidth / 2, borderWidth / 2, this.width - borderWidth, this.height - borderWidth);\n        // Inner border\n        this.ctx.strokeStyle = \"#FFA500\";\n        this.ctx.lineWidth = 5;\n        this.ctx.strokeRect(borderWidth + 5, borderWidth + 5, this.width - (borderWidth + 5) * 2, this.height - (borderWidth + 5) * 2);\n    }\n    drawPlayerName(name) {\n        // Draw background for name\n        const nameY = this.height - 80;\n        const nameHeight = 50;\n        this.ctx.fillStyle = \"rgba(0, 0, 0, 0.7)\";\n        this.ctx.fillRect(0, nameY, this.width, nameHeight);\n        // Draw name text\n        this.ctx.fillStyle = \"#FFFFFF\";\n        this.ctx.font = \"bold 28px Arial\";\n        this.ctx.textAlign = \"center\";\n        this.ctx.textBaseline = \"middle\";\n        this.ctx.fillText(name, this.width / 2, nameY + nameHeight / 2);\n    }\n    async drawMasteryLevel(imagePath) {\n        try {\n            const masteryImage = await (0,canvas__WEBPACK_IMPORTED_MODULE_0__.loadImage)(imagePath);\n            const size = 60;\n            const x = this.width - size - 20;\n            const y = 20;\n            this.ctx.drawImage(masteryImage, x, y, size, size);\n        } catch (error) {\n            console.error(\"Error loading mastery image:\", error);\n        }\n    }\n    async drawSpell(imagePath) {\n        try {\n            const spellImage = await (0,canvas__WEBPACK_IMPORTED_MODULE_0__.loadImage)(imagePath);\n            const size = 50;\n            const x = 20;\n            const y = this.height - size - 20;\n            this.ctx.drawImage(spellImage, x, y, size, size);\n        } catch (error) {\n            console.error(\"Error loading spell image:\", error);\n        }\n    }\n    async drawRank(imagePath, rankNumber) {\n        try {\n            const rankImage = await (0,canvas__WEBPACK_IMPORTED_MODULE_0__.loadImage)(imagePath);\n            const size = 60;\n            const x = 20;\n            const y = 20;\n            this.ctx.drawImage(rankImage, x, y, size, size);\n            // Draw rank number if provided\n            if (rankNumber) {\n                this.ctx.fillStyle = \"#FFFFFF\";\n                this.ctx.font = \"bold 16px Arial\";\n                this.ctx.textAlign = \"center\";\n                this.ctx.fillText(rankNumber.toString(), x + size / 2, y + size + 20);\n            }\n        } catch (error) {\n            console.error(\"Error loading rank image:\", error);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/image-generator.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();