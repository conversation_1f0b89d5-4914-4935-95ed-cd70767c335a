"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.mjs\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.mjs\");\n/* harmony import */ var _barrel_optimize_names_Download_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Loader2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.mjs\");\n/* harmony import */ var _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/frame-data */ \"(app-pages-browser)/./src/lib/frame-data.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    var _config_frameType, _config_frameType1, _config_frameType2, _config_hero, _config_skin, _config_masteryLevel, _config_spell, _config_rank;\n    _s();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        frameType: null,\n        hero: null,\n        skin: null,\n        playerName: \"\",\n        companion: null,\n        masteryLevel: null,\n        spell: null,\n        rank: null,\n        rankNumber: undefined\n    });\n    const [heroes, setHeroes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [skins, setSkins] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generating, setGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedImage, setGeneratedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch heroes on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchHeroes = async ()=>{\n            setLoading(true);\n            try {\n                const response = await fetch(\"/api/heroes\");\n                const data = await response.json();\n                setHeroes(data);\n            } catch (error) {\n                console.error(\"Error fetching heroes:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchHeroes();\n    }, []);\n    // Fetch skins when hero changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSkins = async ()=>{\n            if (!config.hero) {\n                setSkins([]);\n                return;\n            }\n            setLoading(true);\n            try {\n                const response = await fetch(\"/api/skins/\".concat(config.hero.slug));\n                const data = await response.json();\n                setSkins(data);\n            } catch (error) {\n                console.error(\"Error fetching skins:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSkins();\n    }, [\n        config.hero\n    ]);\n    const handleGenerateImage = async ()=>{\n        if (!config.frameType || !config.hero || !config.skin || !config.playerName.trim()) {\n            alert(\"Vui l\\xf2ng điền đầy đủ th\\xf4ng tin: Loại khung, Tướng, Skin v\\xe0 T\\xean người chơi\");\n            return;\n        }\n        setGenerating(true);\n        try {\n            const response = await fetch(\"/api/generate-image\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(config)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to generate image\");\n            }\n            const blob = await response.blob();\n            const imageUrl = URL.createObjectURL(blob);\n            setGeneratedImage(imageUrl);\n        } catch (error) {\n            console.error(\"Error generating image:\", error);\n            alert(\"C\\xf3 lỗi xảy ra khi tạo ảnh. Vui l\\xf2ng thử lại.\");\n        } finally{\n            setGenerating(false);\n        }\n    };\n    const handleDownload = ()=>{\n        if (generatedImage) {\n            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.downloadImage)(generatedImage, \"aov-frame-\".concat(config.playerName || \"player\", \".png\"));\n        }\n    };\n    const oldFramesWithBorder = _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.frameTypes.filter((f)=>f.category === \"old\" && f.hasBorder);\n    const oldFramesWithoutBorder = _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.frameTypes.filter((f)=>f.category === \"old\" && !f.hasBorder);\n    const newFrames = _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.frameTypes.filter((f)=>f.category === \"new\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-white mb-4 flex items-center justify-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-yellow-400\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                \"AOV Frame Generator\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-yellow-400\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-300\",\n                            children: \"Tạo khung h\\xecnh ảnh Li\\xean Qu\\xe2n Mobile một c\\xe1ch dễ d\\xe0ng v\\xe0 miễn ph\\xed\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"aov-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-white\",\n                                                children: \"Chọn loại khung\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-300 mb-2 block\",\n                                                            children: \"Khung cũ - C\\xf3 viền v\\xe0ng\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: ((_config_frameType = config.frameType) === null || _config_frameType === void 0 ? void 0 : _config_frameType.id) || \"\",\n                                                            onValueChange: (value)=>{\n                                                                const frameType = _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.frameTypes.find((f)=>f.id === value);\n                                                                setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        frameType: frameType || null\n                                                                    }));\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                        placeholder: \"Chọn khung c\\xf3 viền\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 151,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 150,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                    children: oldFramesWithBorder.map((frame)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: frame.id,\n                                                                            children: frame.name\n                                                                        }, frame.id, false, {\n                                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 155,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-300 mb-2 block\",\n                                                            children: \"Khung cũ - Kh\\xf4ng viền v\\xe0ng\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: ((_config_frameType1 = config.frameType) === null || _config_frameType1 === void 0 ? void 0 : _config_frameType1.id) || \"\",\n                                                            onValueChange: (value)=>{\n                                                                const frameType = _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.frameTypes.find((f)=>f.id === value);\n                                                                setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        frameType: frameType || null\n                                                                    }));\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                        placeholder: \"Chọn khung kh\\xf4ng viền\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 175,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                    children: oldFramesWithoutBorder.map((frame)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: frame.id,\n                                                                            children: frame.name\n                                                                        }, frame.id, false, {\n                                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 179,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-300 mb-2 block\",\n                                                            children: \"Khung mới\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: ((_config_frameType2 = config.frameType) === null || _config_frameType2 === void 0 ? void 0 : _config_frameType2.id) || \"\",\n                                                            onValueChange: (value)=>{\n                                                                const frameType = _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.frameTypes.find((f)=>f.id === value);\n                                                                setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        frameType: frameType || null\n                                                                    }));\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                        placeholder: \"Chọn khung mới\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                    children: newFrames.map((frame)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: frame.id,\n                                                                            children: [\n                                                                                frame.name,\n                                                                                \" \",\n                                                                                frame.hasBorder ? \"(C\\xf3 viền)\" : \"(Kh\\xf4ng viền)\"\n                                                                            ]\n                                                                        }, frame.id, true, {\n                                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 203,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"aov-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-white\",\n                                                children: \"Chọn tướng v\\xe0 skin\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-300 mb-2 block\",\n                                                            children: \"Chọn tướng\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: ((_config_hero = config.hero) === null || _config_hero === void 0 ? void 0 : _config_hero.id) || \"\",\n                                                            onValueChange: (value)=>{\n                                                                const hero = heroes.find((h)=>h.id === value);\n                                                                setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        hero: hero || null,\n                                                                        skin: null\n                                                                    }));\n                                                            },\n                                                            disabled: loading,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                        placeholder: loading ? \"Đang tải...\" : \"Chọn tướng\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                    children: heroes.map((hero)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: hero.id,\n                                                                            children: hero.name\n                                                                        }, hero.id, false, {\n                                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-300 mb-2 block\",\n                                                            children: \"Chọn skin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: ((_config_skin = config.skin) === null || _config_skin === void 0 ? void 0 : _config_skin.id) || \"\",\n                                                            onValueChange: (value)=>{\n                                                                const skin = skins.find((s)=>s.id === value);\n                                                                setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        skin: skin || null\n                                                                    }));\n                                                            },\n                                                            disabled: !config.hero || loading,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                        placeholder: !config.hero ? \"Chọn tướng trước\" : loading ? \"Đang tải...\" : \"Chọn skin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                    children: skins.map((skin)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: skin.id,\n                                                                            children: [\n                                                                                skin.name,\n                                                                                \" \",\n                                                                                skin.rarity && \"(\".concat(skin.rarity, \")\")\n                                                                            ]\n                                                                        }, skin.id, true, {\n                                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"aov-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-white\",\n                                                children: \"T\\xf9y chọn bổ sung\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-300 mb-2 block\",\n                                                            children: \"Th\\xf4ng thạo / Cục top\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: ((_config_masteryLevel = config.masteryLevel) === null || _config_masteryLevel === void 0 ? void 0 : _config_masteryLevel.id) || \"none\",\n                                                            onValueChange: (value)=>{\n                                                                const mastery = _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.masteryLevels.find((m)=>m.id === value);\n                                                                setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        masteryLevel: mastery || null\n                                                                    }));\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                        placeholder: \"Chọn th\\xf4ng thạo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                    children: _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.masteryLevels.map((mastery)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: mastery.id,\n                                                                            children: mastery.name\n                                                                        }, mastery.id, false, {\n                                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 297,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-300 mb-2 block\",\n                                                            children: \"Ph\\xe9p bổ trợ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: ((_config_spell = config.spell) === null || _config_spell === void 0 ? void 0 : _config_spell.id) || \"none\",\n                                                            onValueChange: (value)=>{\n                                                                const spell = _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.spells.find((s)=>s.id === value);\n                                                                setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        spell: spell || null\n                                                                    }));\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                        placeholder: \"Chọn ph\\xe9p bổ trợ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                    children: _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.spells.map((spell)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: spell.id,\n                                                                            children: spell.name\n                                                                        }, spell.id, false, {\n                                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-300 mb-2 block\",\n                                                            children: \"Thứ hạng\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: ((_config_rank = config.rank) === null || _config_rank === void 0 ? void 0 : _config_rank.id) || \"none\",\n                                                            onValueChange: (value)=>{\n                                                                const rank = _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.ranks.find((r)=>r.id === value);\n                                                                setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        rank: rank || null\n                                                                    }));\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                        placeholder: \"Chọn thứ hạng\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                    children: _lib_frame_data__WEBPACK_IMPORTED_MODULE_6__.ranks.map((rank)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                            value: rank.id,\n                                                                            children: rank.name\n                                                                        }, rank.id, false, {\n                                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this),\n                                                config.rank && config.rank.id !== \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-300 mb-2 block\",\n                                                            children: \"Số thứ hạng (t\\xf9y chọn)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            value: config.rankNumber || \"\",\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        rankNumber: e.target.value ? parseInt(e.target.value) : undefined\n                                                                    })),\n                                                            placeholder: \"Nhập số thứ hạng\",\n                                                            className: \"bg-white/10 border-white/20 text-white placeholder:text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"aov-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-white\",\n                                                children: \"Th\\xf4ng tin người chơi\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-300 mb-2 block\",\n                                                        children: \"T\\xean người chơi\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        value: config.playerName,\n                                                        onChange: (e)=>setConfig((prev)=>({\n                                                                    ...prev,\n                                                                    playerName: e.target.value\n                                                                })),\n                                                        placeholder: \"Nhập t\\xean của bạn\",\n                                                        className: \"bg-white/10 border-white/20 text-white placeholder:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"aov-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-white\",\n                                            children: \"Xem trước\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-800 rounded-lg flex items-center justify-center mb-4\",\n                                                children: generatedImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: generatedImage,\n                                                    alt: \"Generated frame\",\n                                                    className: \"w-full h-full object-cover rounded-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Ảnh sẽ hiển thị ở đ\\xe2y\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: handleGenerateImage,\n                                                        disabled: generating || !config.frameType || !config.hero || !config.skin || !config.playerName.trim(),\n                                                        className: \"w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700\",\n                                                        children: generating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Đang tạo ảnh...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Tạo ảnh\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    generatedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: handleDownload,\n                                                        variant: \"outline\",\n                                                        className: \"w-full border-white/20 text-white hover:bg-white/10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Tải xuống\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"AGbnNJEH53jILhN8JkWnPXsAI/Y=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});