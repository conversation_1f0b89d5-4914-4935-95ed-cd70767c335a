// Generate simple placeholder images for elements when actual images are not available
export function generatePlaceholderImage(text: string, color: string, size: number = 64): string {
  const canvas = document.createElement('canvas');
  canvas.width = size;
  canvas.height = size;
  const ctx = canvas.getContext('2d');
  
  if (!ctx) return '';
  
  // Background
  ctx.fillStyle = color;
  ctx.fillRect(0, 0, size, size);
  
  // Border
  ctx.strokeStyle = '#ffffff';
  ctx.lineWidth = 2;
  ctx.strokeRect(1, 1, size - 2, size - 2);
  
  // Text
  ctx.fillStyle = '#ffffff';
  ctx.font = `bold ${size * 0.4}px Arial`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(text, size / 2, size / 2);
  
  return canvas.toDataURL();
}

export const placeholderImages = {
  mastery: {
    d: generatePlaceholderImage('D', '#8B4513'),
    c: generatePlaceholderImage('C', '#CD7F32'),
    b: generatePlaceholderImage('B', '#C0C0C0'),
    a: generatePlaceholderImage('A', '#FFD700'),
    s: generatePlaceholderImage('S', '#FF6B6B'),
    green: generatePlaceholderImage('G', '#4ECDC4'),
    blue: generatePlaceholderImage('B', '#45B7D1'),
    purple: generatePlaceholderImage('P', '#9B59B6'),
    yellow: generatePlaceholderImage('Y', '#F39C12'),
  },
  spells: {
    bocpha: generatePlaceholderImage('BP', '#FF4444'),
    capcuu: generatePlaceholderImage('CC', '#44FF44'),
    gamthet: generatePlaceholderImage('GT', '#FF8844'),
    ngatngu: generatePlaceholderImage('NN', '#4488FF'),
    suynhuoc: generatePlaceholderImage('SN', '#8844FF'),
    thanhtay: generatePlaceholderImage('TT', '#FFFF44'),
    tocbien: generatePlaceholderImage('TB', '#44FFFF'),
    tochanh: generatePlaceholderImage('TH', '#FF44FF'),
    trungtri: generatePlaceholderImage('TR', '#888888'),
  },
  ranks: {
    bronze: generatePlaceholderImage('BR', '#CD7F32'),
    silver: generatePlaceholderImage('SI', '#C0C0C0'),
    gold: generatePlaceholderImage('GO', '#FFD700'),
    platinum: generatePlaceholderImage('PL', '#E5E4E2'),
    diamond: generatePlaceholderImage('DI', '#B9F2FF'),
    master: generatePlaceholderImage('MA', '#FF6B6B'),
    grandmaster: generatePlaceholderImage('GM', '#FF1493'),
    challenger: generatePlaceholderImage('CH', '#9400D3'),
  }
};
