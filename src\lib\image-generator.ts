import { createCanvas, loadImage, Canvas, CanvasRenderingContext2D } from 'canvas';
import { FrameConfig } from '@/types/aov';
import path from 'path';
import fs from 'fs';

export class ImageGenerator {
  private canvas: Canvas;
  private ctx: CanvasRenderingContext2D;
  private readonly width = 800;
  private readonly height = 800;

  constructor() {
    this.canvas = createCanvas(this.width, this.height);
    this.ctx = this.canvas.getContext('2d');
  }

  async generateFrame(config: FrameConfig): Promise<Buffer> {
    // Clear canvas
    this.ctx.fillStyle = '#000000';
    this.ctx.fillRect(0, 0, this.width, this.height);

    try {
      // Load and draw skin image
      if (config.skin?.image) {
        await this.drawSkinImage(config.skin.image);
      }

      // Load and draw frame
      if (config.frameType) {
        await this.drawFrame(config.frameType.path);
      }

      // Draw player name
      if (config.playerName) {
        this.drawPlayerName(config.playerName);
      }

      // Draw mastery level
      if (config.masteryLevel && config.masteryLevel.id !== 'none') {
        await this.drawMasteryLevel(config.masteryLevel.image);
      }

      // Draw spell
      if (config.spell && config.spell.id !== 'none') {
        await this.drawSpell(config.spell.image);
      }

      // Draw rank
      if (config.rank && config.rank.id !== 'none') {
        await this.drawRank(config.rank.image, config.rankNumber);
      }

      return this.canvas.toBuffer('image/png');
    } catch (error) {
      console.error('Error generating frame:', error);
      throw new Error('Failed to generate frame');
    }
  }

  private async drawSkinImage(imageUrl: string): Promise<void> {
    try {
      // Add headers to avoid CORS issues
      const image = await loadImage(imageUrl);

      // Calculate dimensions to fit the canvas while maintaining aspect ratio
      const aspectRatio = image.width / image.height;
      let drawWidth = this.width;
      let drawHeight = this.height;

      if (aspectRatio > 1) {
        drawHeight = this.width / aspectRatio;
      } else {
        drawWidth = this.height * aspectRatio;
      }

      const x = (this.width - drawWidth) / 2;
      const y = (this.height - drawHeight) / 2;

      this.ctx.drawImage(image, x, y, drawWidth, drawHeight);
    } catch (error) {
      console.error('Error loading skin image:', error);
      // Draw a more attractive placeholder
      this.drawPlaceholder('Skin Image');
    }
  }

  private drawPlaceholder(text: string): void {
    // Gradient background
    const gradient = this.ctx.createLinearGradient(0, 0, this.width, this.height);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Border
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 4;
    this.ctx.strokeRect(2, 2, this.width - 4, this.height - 4);

    // Text
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 32px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(text, this.width / 2, this.height / 2);
  }

  private async drawFrame(framePath: string): Promise<void> {
    try {
      // Try to load frame from local assets
      const frameImagePath = path.join(process.cwd(), 'public', framePath, 'frame.png');

      if (fs.existsSync(frameImagePath)) {
        const frameImage = await loadImage(frameImagePath);
        this.ctx.drawImage(frameImage, 0, 0, this.width, this.height);
      } else {
        // Draw a simple frame border if no frame image exists
        this.drawSimpleFrame();
      }
    } catch (error) {
      console.error('Error loading frame:', error);
      this.drawSimpleFrame();
    }
  }

  private drawSimpleFrame(): void {
    // Draw a simple golden frame
    const borderWidth = 20;

    // Outer border
    this.ctx.strokeStyle = '#FFD700';
    this.ctx.lineWidth = borderWidth;
    this.ctx.strokeRect(borderWidth / 2, borderWidth / 2, this.width - borderWidth, this.height - borderWidth);

    // Inner border
    this.ctx.strokeStyle = '#FFA500';
    this.ctx.lineWidth = 5;
    this.ctx.strokeRect(borderWidth + 5, borderWidth + 5, this.width - (borderWidth + 5) * 2, this.height - (borderWidth + 5) * 2);
  }

  private drawPlayerName(name: string): void {
    // Draw background for name
    const nameY = this.height - 80;
    const nameHeight = 50;

    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, nameY, this.width, nameHeight);

    // Draw name text
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = 'bold 28px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(name, this.width / 2, nameY + nameHeight / 2);
  }

  private async drawMasteryLevel(imagePath: string): Promise<void> {
    try {
      const masteryImage = await loadImage(imagePath);
      const size = 60;
      const x = this.width - size - 20;
      const y = 20;

      this.ctx.drawImage(masteryImage, x, y, size, size);
    } catch (error) {
      console.error('Error loading mastery image:', error);
    }
  }

  private async drawSpell(imagePath: string): Promise<void> {
    try {
      const spellImage = await loadImage(imagePath);
      const size = 50;
      const x = 20;
      const y = this.height - size - 20;

      this.ctx.drawImage(spellImage, x, y, size, size);
    } catch (error) {
      console.error('Error loading spell image:', error);
    }
  }

  private async drawRank(imagePath: string, rankNumber?: number): Promise<void> {
    try {
      const rankImage = await loadImage(imagePath);
      const size = 60;
      const x = 20;
      const y = 20;

      this.ctx.drawImage(rankImage, x, y, size, size);

      // Draw rank number if provided
      if (rankNumber) {
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 16px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(rankNumber.toString(), x + size / 2, y + size + 20);
      }
    } catch (error) {
      console.error('Error loading rank image:', error);
    }
  }
}
