import { createCanvas, loadImage, Canvas, CanvasRenderingContext2D } from 'canvas';
import { FrameConfig } from '@/types/aov';
import path from 'path';
import fs from 'fs';

export class ImageGenerator {
  private canvas: Canvas;
  private ctx: CanvasRenderingContext2D;
  private readonly width = 800;
  private readonly height = 800;

  constructor() {
    this.canvas = createCanvas(this.width, this.height);
    this.ctx = this.canvas.getContext('2d');
  }

  async generateFrame(config: FrameConfig): Promise<Buffer> {
    // Clear canvas
    this.ctx.fillStyle = '#000000';
    this.ctx.fillRect(0, 0, this.width, this.height);

    try {
      // Load and draw skin image
      if (config.skin?.image) {
        await this.drawSkinImage(config.skin.image);
      }

      // Load and draw frame
      if (config.frameType) {
        await this.drawFrame(config.frameType.path);
      }

      // Draw player name
      if (config.playerName) {
        this.drawPlayerName(config.playerName);
      }

      // Draw mastery level
      if (config.masteryLevel && config.masteryLevel.id !== 'none') {
        await this.drawMasteryLevel(config.masteryLevel.image);
      }

      // Draw spell
      if (config.spell && config.spell.id !== 'none') {
        await this.drawSpell(config.spell.image);
      }

      // Draw rank
      if (config.rank && config.rank.id !== 'none') {
        await this.drawRank(config.rank.image, config.rankNumber);
      }

      return this.canvas.toBuffer('image/png');
    } catch (error) {
      console.error('Error generating frame:', error);
      throw new Error('Failed to generate frame');
    }
  }

  private async drawSkinImage(imageUrl: string): Promise<void> {
    try {
      // Add headers to avoid CORS issues
      const image = await loadImage(imageUrl);

      // Strategy: Fill the entire canvas with the skin image (crop to fit)
      // This ensures the skin image covers the full frame area
      const imageAspectRatio = image.width / image.height;
      const canvasAspectRatio = this.width / this.height;

      let drawWidth, drawHeight, x, y;

      if (imageAspectRatio > canvasAspectRatio) {
        // Image is wider than canvas - fit by height and crop sides
        drawHeight = this.height;
        drawWidth = this.height * imageAspectRatio;
        x = (this.width - drawWidth) / 2;
        y = 0;
      } else {
        // Image is taller than canvas - fit by width and crop top/bottom
        drawWidth = this.width;
        drawHeight = this.width / imageAspectRatio;
        x = 0;
        y = (this.height - drawHeight) / 2;
      }

      // Draw the skin image to fill the entire canvas
      this.ctx.drawImage(image, x, y, drawWidth, drawHeight);
    } catch (error) {
      console.error('Error loading skin image:', error);
      // Draw a more attractive placeholder
      this.drawPlaceholder('Skin Image');
    }
  }

  private drawPlaceholder(text: string): void {
    // Gradient background
    const gradient = this.ctx.createLinearGradient(0, 0, this.width, this.height);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Border
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 4;
    this.ctx.strokeRect(2, 2, this.width - 4, this.height - 4);

    // Text
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 32px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(text, this.width / 2, this.height / 2);
  }

  private async drawFrame(framePath: string): Promise<void> {
    try {
      // Try to load frame from local assets
      const frameImagePath = path.join(process.cwd(), 'public', framePath, 'frame.png');

      if (fs.existsSync(frameImagePath)) {
        const frameImage = await loadImage(frameImagePath);
        this.ctx.drawImage(frameImage, 0, 0, this.width, this.height);
      } else {
        // Draw a simple frame border if no frame image exists
        this.drawSimpleFrame();
      }
    } catch (error) {
      console.error('Error loading frame:', error);
      this.drawSimpleFrame();
    }
  }

  private drawSimpleFrame(): void {
    // Draw a more sophisticated frame that doesn't obscure the skin image
    const borderWidth = 15;

    // Create gradient for frame
    const gradient = this.ctx.createLinearGradient(0, 0, this.width, this.height);
    gradient.addColorStop(0, '#FFD700');
    gradient.addColorStop(0.5, '#FFA500');
    gradient.addColorStop(1, '#FF8C00');

    // Draw frame border
    this.ctx.strokeStyle = gradient;
    this.ctx.lineWidth = borderWidth;
    this.ctx.strokeRect(borderWidth / 2, borderWidth / 2, this.width - borderWidth, this.height - borderWidth);

    // Add inner glow effect
    this.ctx.shadowColor = '#FFD700';
    this.ctx.shadowBlur = 10;
    this.ctx.strokeStyle = '#FFFFFF';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(borderWidth + 2, borderWidth + 2, this.width - (borderWidth + 2) * 2, this.height - (borderWidth + 2) * 2);

    // Reset shadow
    this.ctx.shadowColor = 'transparent';
    this.ctx.shadowBlur = 0;
  }

  private drawPlayerName(name: string): void {
    // Draw a more attractive name banner
    const nameY = this.height - 70;
    const nameHeight = 40;
    const padding = 20;

    // Create gradient background for name
    const gradient = this.ctx.createLinearGradient(0, nameY, 0, nameY + nameHeight);
    gradient.addColorStop(0, 'rgba(0, 0, 0, 0.8)');
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0.6)');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(padding, nameY, this.width - padding * 2, nameHeight);

    // Add border to name banner
    this.ctx.strokeStyle = '#FFD700';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(padding, nameY, this.width - padding * 2, nameHeight);

    // Draw name text with shadow effect
    this.ctx.shadowColor = 'rgba(0, 0, 0, 0.8)';
    this.ctx.shadowOffsetX = 2;
    this.ctx.shadowOffsetY = 2;
    this.ctx.shadowBlur = 4;

    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = 'bold 24px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(name, this.width / 2, nameY + nameHeight / 2);

    // Reset shadow
    this.ctx.shadowColor = 'transparent';
    this.ctx.shadowOffsetX = 0;
    this.ctx.shadowOffsetY = 0;
    this.ctx.shadowBlur = 0;
  }

  private async drawMasteryLevel(imagePath: string): Promise<void> {
    try {
      const masteryImage = await loadImage(imagePath);
      const size = 60;
      const x = this.width - size - 20;
      const y = 20;

      this.ctx.drawImage(masteryImage, x, y, size, size);
    } catch (error) {
      console.error('Error loading mastery image:', error);
    }
  }

  private async drawSpell(imagePath: string): Promise<void> {
    try {
      const spellImage = await loadImage(imagePath);
      const size = 50;
      const x = 20;
      const y = this.height - size - 20;

      this.ctx.drawImage(spellImage, x, y, size, size);
    } catch (error) {
      console.error('Error loading spell image:', error);
    }
  }

  private async drawRank(imagePath: string, rankNumber?: number): Promise<void> {
    try {
      const rankImage = await loadImage(imagePath);
      const size = 60;
      const x = 20;
      const y = 20;

      this.ctx.drawImage(rankImage, x, y, size, size);

      // Draw rank number if provided
      if (rankNumber) {
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 16px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(rankNumber.toString(), x + size / 2, y + size + 20);
      }
    } catch (error) {
      console.error('Error loading rank image:', error);
    }
  }
}
