"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse5-htmlparser2-tree-adapter";
exports.ids = ["vendor-chunks/parse5-htmlparser2-tree-adapter"];
exports.modules = {

/***/ "(rsc)/./node_modules/parse5-htmlparser2-tree-adapter/dist/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/parse5-htmlparser2-tree-adapter/dist/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adapter: () => (/* binding */ adapter),\n/* harmony export */   serializeDoctypeContent: () => (/* binding */ serializeDoctypeContent)\n/* harmony export */ });\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! parse5 */ \"(rsc)/./node_modules/parse5/dist/index.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n\nfunction enquoteDoctypeId(id) {\n    const quote = id.includes('\"') ? \"'\" : '\"';\n    return quote + id + quote;\n}\n/** @internal */\nfunction serializeDoctypeContent(name, publicId, systemId) {\n    let str = '!DOCTYPE ';\n    if (name) {\n        str += name;\n    }\n    if (publicId) {\n        str += ` PUBLIC ${enquoteDoctypeId(publicId)}`;\n    }\n    else if (systemId) {\n        str += ' SYSTEM';\n    }\n    if (systemId) {\n        str += ` ${enquoteDoctypeId(systemId)}`;\n    }\n    return str;\n}\nconst adapter = {\n    // Re-exports from domhandler\n    isCommentNode: domhandler__WEBPACK_IMPORTED_MODULE_1__.isComment,\n    isElementNode: domhandler__WEBPACK_IMPORTED_MODULE_1__.isTag,\n    isTextNode: domhandler__WEBPACK_IMPORTED_MODULE_1__.isText,\n    //Node construction\n    createDocument() {\n        const node = new domhandler__WEBPACK_IMPORTED_MODULE_1__.Document([]);\n        node['x-mode'] = parse5__WEBPACK_IMPORTED_MODULE_0__.html.DOCUMENT_MODE.NO_QUIRKS;\n        return node;\n    },\n    createDocumentFragment() {\n        return new domhandler__WEBPACK_IMPORTED_MODULE_1__.Document([]);\n    },\n    createElement(tagName, namespaceURI, attrs) {\n        const attribs = Object.create(null);\n        const attribsNamespace = Object.create(null);\n        const attribsPrefix = Object.create(null);\n        for (let i = 0; i < attrs.length; i++) {\n            const attrName = attrs[i].name;\n            attribs[attrName] = attrs[i].value;\n            attribsNamespace[attrName] = attrs[i].namespace;\n            attribsPrefix[attrName] = attrs[i].prefix;\n        }\n        const node = new domhandler__WEBPACK_IMPORTED_MODULE_1__.Element(tagName, attribs, []);\n        node.namespace = namespaceURI;\n        node['x-attribsNamespace'] = attribsNamespace;\n        node['x-attribsPrefix'] = attribsPrefix;\n        return node;\n    },\n    createCommentNode(data) {\n        return new domhandler__WEBPACK_IMPORTED_MODULE_1__.Comment(data);\n    },\n    createTextNode(value) {\n        return new domhandler__WEBPACK_IMPORTED_MODULE_1__.Text(value);\n    },\n    //Tree mutation\n    appendChild(parentNode, newNode) {\n        const prev = parentNode.children[parentNode.children.length - 1];\n        if (prev) {\n            prev.next = newNode;\n            newNode.prev = prev;\n        }\n        parentNode.children.push(newNode);\n        newNode.parent = parentNode;\n    },\n    insertBefore(parentNode, newNode, referenceNode) {\n        const insertionIdx = parentNode.children.indexOf(referenceNode);\n        const { prev } = referenceNode;\n        if (prev) {\n            prev.next = newNode;\n            newNode.prev = prev;\n        }\n        referenceNode.prev = newNode;\n        newNode.next = referenceNode;\n        parentNode.children.splice(insertionIdx, 0, newNode);\n        newNode.parent = parentNode;\n    },\n    setTemplateContent(templateElement, contentElement) {\n        adapter.appendChild(templateElement, contentElement);\n    },\n    getTemplateContent(templateElement) {\n        return templateElement.children[0];\n    },\n    setDocumentType(document, name, publicId, systemId) {\n        const data = serializeDoctypeContent(name, publicId, systemId);\n        let doctypeNode = document.children.find((node) => (0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isDirective)(node) && node.name === '!doctype');\n        if (doctypeNode) {\n            doctypeNode.data = data !== null && data !== void 0 ? data : null;\n        }\n        else {\n            doctypeNode = new domhandler__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction('!doctype', data);\n            adapter.appendChild(document, doctypeNode);\n        }\n        doctypeNode['x-name'] = name;\n        doctypeNode['x-publicId'] = publicId;\n        doctypeNode['x-systemId'] = systemId;\n    },\n    setDocumentMode(document, mode) {\n        document['x-mode'] = mode;\n    },\n    getDocumentMode(document) {\n        return document['x-mode'];\n    },\n    detachNode(node) {\n        if (node.parent) {\n            const idx = node.parent.children.indexOf(node);\n            const { prev, next } = node;\n            node.prev = null;\n            node.next = null;\n            if (prev) {\n                prev.next = next;\n            }\n            if (next) {\n                next.prev = prev;\n            }\n            node.parent.children.splice(idx, 1);\n            node.parent = null;\n        }\n    },\n    insertText(parentNode, text) {\n        const lastChild = parentNode.children[parentNode.children.length - 1];\n        if (lastChild && (0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isText)(lastChild)) {\n            lastChild.data += text;\n        }\n        else {\n            adapter.appendChild(parentNode, adapter.createTextNode(text));\n        }\n    },\n    insertTextBefore(parentNode, text, referenceNode) {\n        const prevNode = parentNode.children[parentNode.children.indexOf(referenceNode) - 1];\n        if (prevNode && (0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isText)(prevNode)) {\n            prevNode.data += text;\n        }\n        else {\n            adapter.insertBefore(parentNode, adapter.createTextNode(text), referenceNode);\n        }\n    },\n    adoptAttributes(recipient, attrs) {\n        for (let i = 0; i < attrs.length; i++) {\n            const attrName = attrs[i].name;\n            if (recipient.attribs[attrName] === undefined) {\n                recipient.attribs[attrName] = attrs[i].value;\n                recipient['x-attribsNamespace'][attrName] = attrs[i].namespace;\n                recipient['x-attribsPrefix'][attrName] = attrs[i].prefix;\n            }\n        }\n    },\n    //Tree traversing\n    getFirstChild(node) {\n        return node.children[0];\n    },\n    getChildNodes(node) {\n        return node.children;\n    },\n    getParentNode(node) {\n        return node.parent;\n    },\n    getAttrList(element) {\n        return element.attributes;\n    },\n    //Node data\n    getTagName(element) {\n        return element.name;\n    },\n    getNamespaceURI(element) {\n        return element.namespace;\n    },\n    getTextNodeContent(textNode) {\n        return textNode.data;\n    },\n    getCommentNodeContent(commentNode) {\n        return commentNode.data;\n    },\n    getDocumentTypeNodeName(doctypeNode) {\n        var _a;\n        return (_a = doctypeNode['x-name']) !== null && _a !== void 0 ? _a : '';\n    },\n    getDocumentTypeNodePublicId(doctypeNode) {\n        var _a;\n        return (_a = doctypeNode['x-publicId']) !== null && _a !== void 0 ? _a : '';\n    },\n    getDocumentTypeNodeSystemId(doctypeNode) {\n        var _a;\n        return (_a = doctypeNode['x-systemId']) !== null && _a !== void 0 ? _a : '';\n    },\n    //Node types\n    isDocumentTypeNode(node) {\n        return (0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isDirective)(node) && node.name === '!doctype';\n    },\n    // Source code location\n    setNodeSourceCodeLocation(node, location) {\n        if (location) {\n            node.startIndex = location.startOffset;\n            node.endIndex = location.endOffset;\n        }\n        node.sourceCodeLocation = location;\n    },\n    getNodeSourceCodeLocation(node) {\n        return node.sourceCodeLocation;\n    },\n    updateNodeSourceCodeLocation(node, endLocation) {\n        if (endLocation.endOffset != null)\n            node.endIndex = endLocation.endOffset;\n        node.sourceCodeLocation = {\n            ...node.sourceCodeLocation,\n            ...endLocation,\n        };\n    },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/parse5-htmlparser2-tree-adapter/dist/index.js\n");

/***/ })

};
;