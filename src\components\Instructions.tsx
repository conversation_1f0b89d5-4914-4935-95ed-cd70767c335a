import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Info, CheckCircle } from 'lucide-react';

export const Instructions = () => {
  return (
    <Card className="aov-card mb-6">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Info className="h-5 w-5" />
          Hướng dẫn sử dụng
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-start gap-3">
          <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-gray-300">
            <strong>Bước 1:</strong> Chọ<PERSON> lo<PERSON><PERSON> khung (cũ/mới) và tùy chọn viền
          </p>
        </div>
        <div className="flex items-start gap-3">
          <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-gray-300">
            <strong>Bước 2:</strong> Chọn tướng từ danh sách
          </p>
        </div>
        <div className="flex items-start gap-3">
          <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-gray-300">
            <strong>Bước 3:</strong> Chọn skin tương ứng với tướng
          </p>
        </div>
        <div className="flex items-start gap-3">
          <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-gray-300">
            <strong>Bước 4:</strong> Nhập tên người chơi
          </p>
        </div>
        <div className="flex items-start gap-3">
          <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-gray-300">
            <strong>Bước 5:</strong> Tùy chọn thêm thông thạo, phép bổ trợ, thứ hạng (không bắt buộc)
          </p>
        </div>
        <div className="flex items-start gap-3">
          <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-gray-300">
            <strong>Bước 6:</strong> Nhấn "Tạo ảnh" và tải xuống kết quả
          </p>
        </div>
        
        <div className="mt-4 p-3 bg-blue-900/30 rounded-lg border border-blue-500/30">
          <p className="text-xs text-blue-300">
            <strong>Lưu ý:</strong> Dữ liệu tướng và skin được lấy từ trang chính thức của Garena. 
            Ứng dụng hoàn toàn miễn phí và không có quảng cáo.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
