import axios from 'axios';
import * as cheerio from 'cheerio';
import { <PERSON>, Skin } from '@/types/aov';
import { slugify, formatImageUrl } from './utils';
import { mockHeroes, mockSkins } from './mock-data';

const BASE_URL = 'https://lienquan.garena.vn';

export class AOVDataFetcher {
  private static instance: AOVDataFetcher;
  private heroesCache: Hero[] | null = null;
  private skinsCache: Map<string, Skin[]> = new Map();

  static getInstance(): AOVDataFetcher {
    if (!AOVDataFetcher.instance) {
      AOVDataFetcher.instance = new AOVDataFetcher();
    }
    return AOVDataFetcher.instance;
  }

  async fetchHeroes(): Promise<Hero[]> {
    if (this.heroesCache) {
      return this.heroesCache;
    }

    try {
      const response = await axios.get(`${BASE_URL}/hoc-vien/tuong-skin/`, {
        timeout: 15000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      const $ = cheerio.load(response.data);
      const heroes: Hero[] = [];

      // Tìm tất cả các link tướng trong trang
      $('a[href*="/hoc-vien/tuong-skin/d/"]').each((_, element) => {
        const $el = $(element);
        const link = $el.attr('href') || '';
        const $img = $el.find('img');
        const image = $img.attr('src') || '';

        // Lấy tên từ alt của img hoặc từ text
        let name = $img.attr('alt') || $el.text().trim();

        // Nếu không có tên từ alt, thử lấy từ các element con
        if (!name) {
          name = $el.find('h3, h4, .hero-name').text().trim();
        }

        if (name && image && link) {
          // Extract slug from URL: /hoc-vien/tuong-skin/d/iggy/ -> iggy
          const urlParts = link.split('/');
          const slug = urlParts[urlParts.length - 2] || urlParts[urlParts.length - 1];

          if (slug && !heroes.find(h => h.slug === slug)) {
            heroes.push({
              id: slug,
              name: name.trim(),
              slug,
              image: formatImageUrl(image),
              category: 'hero'
            });
          }
        }
      });

      console.log(`Fetched ${heroes.length} heroes from Garena`);
      this.heroesCache = heroes.length > 0 ? heroes : mockHeroes;
      return this.heroesCache;
    } catch (error) {
      console.error('Error fetching heroes, using mock data:', error);
      this.heroesCache = mockHeroes;
      return mockHeroes;
    }
  }

  async fetchHeroSkins(heroSlug: string): Promise<Skin[]> {
    if (this.skinsCache.has(heroSlug)) {
      return this.skinsCache.get(heroSlug)!;
    }

    try {
      const response = await axios.get(`${BASE_URL}/hoc-vien/tuong-skin/d/${heroSlug}/`, {
        timeout: 15000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      const $ = cheerio.load(response.data);
      const skins: Skin[] = [];

      // Tìm các skin trong phần "Trang phục"
      $('a[href*="#heroSkin-"]').each((index, element) => {
        const $el = $(element);
        const $img = $el.find('img');
        const image = $img.attr('src') || '';
        const name = $img.attr('title') || $img.attr('alt') || '';

        if (name && image) {
          skins.push({
            id: `${heroSlug}-${index}`,
            name: name.trim(),
            heroId: heroSlug,
            image: formatImageUrl(image),
            rarity: this.extractRarity(name, ''),
            label: undefined
          });
        }
      });

      // Nếu không tìm thấy skin từ phần "Trang phục", thử tìm từ các h3 header
      if (skins.length === 0) {
        $('h3').each((index, element) => {
          const $el = $(element);
          const name = $el.text().trim();
          const $img = $el.next('img');
          const image = $img.attr('src') || '';

          // Tìm label từ img trước h3
          const $labelImg = $el.prev('img');
          const label = $labelImg.attr('src') || '';

          if (name && image && name.toLowerCase().includes(heroSlug.toLowerCase())) {
            skins.push({
              id: `${heroSlug}-${index}`,
              name: name.trim(),
              heroId: heroSlug,
              image: formatImageUrl(image),
              rarity: this.extractRarity(name, label),
              label: label ? formatImageUrl(label) : undefined
            });
          }
        });
      }

      console.log(`Fetched ${skins.length} skins for ${heroSlug}`);
      const finalSkins = skins.length > 0 ? skins : (mockSkins[heroSlug] || []);
      this.skinsCache.set(heroSlug, finalSkins);
      return finalSkins;
    } catch (error) {
      console.error(`Error fetching skins for ${heroSlug}, using mock data:`, error);
      const fallbackSkins = mockSkins[heroSlug] || [];
      this.skinsCache.set(heroSlug, fallbackSkins);
      return fallbackSkins;
    }
  }

  private extractRarity(name: string, label: string): string {
    const lowerName = name.toLowerCase();
    const lowerLabel = label.toLowerCase();

    if (lowerLabel.includes('sss') || lowerName.includes('sss')) return 'SSS';
    if (lowerLabel.includes('ss') || lowerName.includes('ss')) return 'SS';
    if (lowerLabel.includes('s+') || lowerName.includes('s+')) return 'S+';
    if (lowerLabel.includes('s') || lowerName.includes('s')) return 'S';
    if (lowerLabel.includes('a') || lowerName.includes('a')) return 'A';

    return 'Normal';
  }

  clearCache(): void {
    this.heroesCache = null;
    this.skinsCache.clear();
  }
}
