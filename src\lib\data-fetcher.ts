import axios from 'axios';
import * as cheerio from 'cheerio';
import { <PERSON>, Skin } from '@/types/aov';
import { slugify, formatImageUrl } from './utils';
import { mockHeroes, mockSkins } from './mock-data';

const BASE_URL = 'https://lienquan.garena.vn';

export class AOVDataFetcher {
  private static instance: AOVDataFetcher;
  private heroesCache: Hero[] | null = null;
  private skinsCache: Map<string, Skin[]> = new Map();

  static getInstance(): AOVDataFetcher {
    if (!AOVDataFetcher.instance) {
      AOVDataFetcher.instance = new AOVDataFetcher();
    }
    return AOVDataFetcher.instance;
  }

  async fetchHeroes(): Promise<Hero[]> {
    if (this.heroesCache) {
      return this.heroesCache;
    }

    try {
      const response = await axios.get(`${BASE_URL}/hoc-vien/tuong-skin/`, {
        timeout: 10000
      });
      const $ = cheerio.load(response.data);
      const heroes: Hero[] = [];

      $('.hero-item, .character-item').each((_, element) => {
        const $el = $(element);
        const name = $el.find('.hero-name, .character-name').text().trim();
        const image = $el.find('img').attr('src') || '';
        const link = $el.find('a').attr('href') || '';

        if (name && image) {
          const slug = link.split('/').pop() || slugify(name);
          heroes.push({
            id: slug,
            name,
            slug,
            image: formatImageUrl(image),
            category: 'hero'
          });
        }
      });

      this.heroesCache = heroes.length > 0 ? heroes : mockHeroes;
      return this.heroesCache;
    } catch (error) {
      console.error('Error fetching heroes, using mock data:', error);
      this.heroesCache = mockHeroes;
      return mockHeroes;
    }
  }

  async fetchHeroSkins(heroSlug: string): Promise<Skin[]> {
    if (this.skinsCache.has(heroSlug)) {
      return this.skinsCache.get(heroSlug)!;
    }

    try {
      const response = await axios.get(`${BASE_URL}/hoc-vien/tuong-skin/d/${heroSlug}/`);
      const $ = cheerio.load(response.data);
      const skins: Skin[] = [];

      $('.skin-item, .hero-skin-item').each((index, element) => {
        const $el = $(element);
        const name = $el.find('.skin-name, h3, h4').text().trim();
        const image = $el.find('img').attr('src') || '';
        const label = $el.find('.skin-label img').attr('src') || '';

        if (name && image) {
          skins.push({
            id: `${heroSlug}-${index}`,
            name,
            heroId: heroSlug,
            image: formatImageUrl(image),
            rarity: this.extractRarity(name, label),
            label: label ? formatImageUrl(label) : undefined
          });
        }
      });

      this.skinsCache.set(heroSlug, skins);
      return skins;
    } catch (error) {
      console.error(`Error fetching skins for ${heroSlug}:`, error);
      return [];
    }
  }

  private extractRarity(name: string, label: string): string {
    const lowerName = name.toLowerCase();
    const lowerLabel = label.toLowerCase();

    if (lowerLabel.includes('sss') || lowerName.includes('sss')) return 'SSS';
    if (lowerLabel.includes('ss') || lowerName.includes('ss')) return 'SS';
    if (lowerLabel.includes('s+') || lowerName.includes('s+')) return 'S+';
    if (lowerLabel.includes('s') || lowerName.includes('s')) return 'S';
    if (lowerLabel.includes('a') || lowerName.includes('a')) return 'A';

    return 'Normal';
  }

  clearCache(): void {
    this.heroesCache = null;
    this.skinsCache.clear();
  }
}
