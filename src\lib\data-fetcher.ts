import axios from 'axios';
import * as cheerio from 'cheerio';
import { <PERSON>, Skin } from '@/types/aov';
import { slugify, formatImageUrl } from './utils';
import { mockHeroes, mockSkins } from './mock-data';

const BASE_URL = 'https://lienquan.garena.vn';

export class AOVDataFetcher {
  private static instance: AOVDataFetcher;
  private heroesCache: Hero[] | null = null;
  private skinsCache: Map<string, Skin[]> = new Map();

  static getInstance(): AOVDataFetcher {
    if (!AOVDataFetcher.instance) {
      AOVDataFetcher.instance = new AOVDataFetcher();
    }
    return AOVDataFetcher.instance;
  }

  async fetchHeroes(): Promise<Hero[]> {
    if (this.heroesCache) {
      return this.heroesCache;
    }

    try {
      const response = await axios.get(`${BASE_URL}/hoc-vien/tuong-skin/`, {
        timeout: 15000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      const $ = cheerio.load(response.data);
      const heroes: Hero[] = [];

      // Tìm tất cả các link tướng trong trang
      $('a[href*="/hoc-vien/tuong-skin/d/"]').each((_, element) => {
        const $el = $(element);
        const link = $el.attr('href') || '';
        const $img = $el.find('img');
        const image = $img.attr('src') || '';

        // Lấy tên từ alt của img hoặc từ text
        let name = $img.attr('alt') || $el.text().trim();

        // Nếu không có tên từ alt, thử lấy từ các element con
        if (!name) {
          name = $el.find('h3, h4, .hero-name').text().trim();
        }

        if (name && image && link) {
          // Extract slug from URL: /hoc-vien/tuong-skin/d/iggy/ -> iggy
          const urlParts = link.split('/');
          const slug = urlParts[urlParts.length - 2] || urlParts[urlParts.length - 1];

          if (slug && !heroes.find(h => h.slug === slug)) {
            heroes.push({
              id: slug,
              name: name.trim(),
              slug,
              image: formatImageUrl(image),
              category: 'hero'
            });
          }
        }
      });

      console.log(`Fetched ${heroes.length} heroes from Garena`);
      this.heroesCache = heroes.length > 0 ? heroes : mockHeroes;
      return this.heroesCache;
    } catch (error) {
      console.error('Error fetching heroes, using mock data:', error);
      this.heroesCache = mockHeroes;
      return mockHeroes;
    }
  }

  async fetchHeroSkins(heroSlug: string): Promise<Skin[]> {
    if (this.skinsCache.has(heroSlug)) {
      return this.skinsCache.get(heroSlug)!;
    }

    try {
      const response = await axios.get(`${BASE_URL}/hoc-vien/tuong-skin/d/${heroSlug}/`, {
        timeout: 15000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      const $ = cheerio.load(response.data);
      const skins: Skin[] = [];

      // Tìm các skin trong div.hero__skins--detail
      $('.hero__skins--detail').each((index, element) => {
        const $el = $(element);
        const $h3 = $el.find('h3');

        // Lấy hình ảnh skin từ picture > img (không phải badge)
        const $skinImg = $el.find('picture img').first();

        const name = $h3.text().trim();
        const image = $skinImg.attr('src') || '';

        // Tìm badge tier (nhãn phẩm chất) - những img không nằm trong picture
        const $labelImg = $el.find('img').not('picture img').filter('[src*="label"], [src*="rank"], [src*="53805"], [src*="53804"], [src*="53803"], [src*="53802"]').first();
        const label = $labelImg.attr('src') || '';

        if (name && image) {
          skins.push({
            id: `${heroSlug}-skin-${index + 1}`,
            name: name.trim(),
            heroId: heroSlug,
            image: formatImageUrl(image),
            rarity: this.extractRarity(name, label),
            label: label ? formatImageUrl(label) : undefined
          });
        }
      });

      // Fallback 1: Tìm từ div có id heroSkin-*
      if (skins.length === 0) {
        $('div[id^="heroSkin-"]').each((index, element) => {
          const $el = $(element);
          const $h3 = $el.find('h3');

          // Ưu tiên lấy từ picture > img trước
          let $skinImg = $el.find('picture img').first();
          if ($skinImg.length === 0) {
            // Nếu không có picture, lấy img đầu tiên (có thể là skin image)
            $skinImg = $el.find('img').first();
          }

          const name = $h3.text().trim();
          const image = $skinImg.attr('src') || '';

          // Tìm badge tier - những img không phải skin image
          const $labelImg = $el.find('img').not('picture img').filter('[src*="label"], [src*="rank"], [src*="53805"], [src*="53804"], [src*="53803"], [src*="53802"]').first();
          const label = $labelImg.attr('src') || '';

          if (name && image) {
            skins.push({
              id: `${heroSlug}-div-${index + 1}`,
              name: name.trim(),
              heroId: heroSlug,
              image: formatImageUrl(image),
              rarity: this.extractRarity(name, label),
              label: label ? formatImageUrl(label) : undefined
            });
          }
        });
      }

      // Fallback 2: Tìm từ các h3 có picture > img kế bên
      if (skins.length === 0) {
        $('h3').each((index, element) => {
          const $el = $(element);
          const name = $el.text().trim();

          // Ưu tiên tìm picture > img trước
          let $skinImg = $el.siblings('picture').find('img').first();
          if ($skinImg.length === 0) {
            $skinImg = $el.parent().find('picture img').first();
          }
          if ($skinImg.length === 0) {
            // Fallback: lấy img đầu tiên không phải badge
            $skinImg = $el.siblings('img').not('[src*="label"]').not('[src*="rank"]').not('[src*="53805"]').not('[src*="53804"]').not('[src*="53803"]').not('[src*="53802"]').first();
          }

          const image = $skinImg.attr('src') || '';

          // Tìm badge tier
          const $labelImg = $el.parent().find('img[src*="label"], img[src*="rank"], img[src*="53805"], img[src*="53804"], img[src*="53803"], img[src*="53802"]').first();
          const label = $labelImg.attr('src') || '';

          if (name && image && name.length > 2) {
            skins.push({
              id: `${heroSlug}-h3-${index + 1}`,
              name: name.trim(),
              heroId: heroSlug,
              image: formatImageUrl(image),
              rarity: this.extractRarity(name, label),
              label: label ? formatImageUrl(label) : undefined
            });
          }
        });
      }

      // Nếu vẫn không có skin, thử tìm từ cấu trúc khác
      if (skins.length === 0) {
        // Tìm tất cả img có src chứa heroSlug hoặc tên tướng
        $('img').each((index, element) => {
          const $img = $(element);
          const image = $img.attr('src') || '';
          const alt = $img.attr('alt') || '';
          const title = $img.attr('title') || '';

          // Kiểm tra xem có phải là skin image không
          if (image && (alt.toLowerCase().includes(heroSlug) || title.toLowerCase().includes(heroSlug))) {
            const name = title || alt || `${heroSlug} Skin ${index + 1}`;

            skins.push({
              id: `${heroSlug}-img-${index + 1}`,
              name: name.trim(),
              heroId: heroSlug,
              image: formatImageUrl(image),
              rarity: this.extractRarity(name, ''),
              label: undefined
            });
          }
        });
      }

      console.log(`Fetched ${skins.length} skins for ${heroSlug}`);
      const finalSkins = skins.length > 0 ? skins : (mockSkins[heroSlug] || []);
      this.skinsCache.set(heroSlug, finalSkins);
      return finalSkins;
    } catch (error) {
      console.error(`Error fetching skins for ${heroSlug}, using mock data:`, error);
      const fallbackSkins = mockSkins[heroSlug] || [];
      this.skinsCache.set(heroSlug, fallbackSkins);
      return fallbackSkins;
    }
  }

  private extractRarity(name: string, label: string): string {
    const lowerName = name.toLowerCase();
    const lowerLabel = label.toLowerCase();

    // Kiểm tra từ label image URL trước
    if (lowerLabel.includes('53805') || lowerLabel.includes('sss')) return 'SSS';
    if (lowerLabel.includes('53803') || lowerLabel.includes('53802') || lowerLabel.includes('ss')) return 'SS';
    if (lowerLabel.includes('s+') || lowerName.includes('s+')) return 'S+';
    if (lowerLabel.includes('53804') || lowerLabel.includes('/s') || lowerName.includes('s') && !lowerName.includes('ss')) return 'S';
    if (lowerLabel.includes('label_a') || lowerLabel.includes('a_new') || lowerName.includes('tiểu hoàng đế')) return 'A';

    // Kiểm tra từ tên skin
    if (lowerName.includes('hỗn mang') || lowerName.includes('tiếng thét')) return 'SSS';
    if (lowerName.includes('thần miêu') || lowerName.includes('bạch hồ ly')) return 'SS';
    if (lowerName.includes('tiểu hoàng đế')) return 'A';

    return 'Normal';
  }

  clearCache(): void {
    this.heroesCache = null;
    this.skinsCache.clear();
  }
}
