import { Heart, Github, Coffee } from 'lucide-react';

export const Footer = () => {
  return (
    <footer className="mt-12 py-8 border-t border-white/10">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-2 text-gray-300">
            <span>Đ<PERSON><PERSON><PERSON> tạo với</span>
            <Heart className="h-4 w-4 text-red-500" />
            <span>bởi cộng đồng AOV</span>
          </div>
          
          <div className="flex items-center justify-center gap-6 text-sm text-gray-400">
            <a 
              href="https://github.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center gap-2 hover:text-white transition-colors"
            >
              <Github className="h-4 w-4" />
              GitHub
            </a>
            <a 
              href="#" 
              className="flex items-center gap-2 hover:text-white transition-colors"
            >
              <Coffee className="h-4 w-4" />
              Ủng hộ
            </a>
          </div>
          
          <div className="text-xs text-gray-500 space-y-2">
            <p>
              Dữ liệu tướng và skin được lấy từ trang chính thức của Garena Liên Quân Mobile.
            </p>
            <p>
              Ứng dụng này không liên kết với Garena và chỉ dành cho mục đích giải trí.
            </p>
            <p>
              © 2024 AOV Frame Generator. Tất cả quyền được bảo lưu.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};
