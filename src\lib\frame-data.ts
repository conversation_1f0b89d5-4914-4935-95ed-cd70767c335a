import { FrameType, Companion, MasteryLevel, Spell, Rank } from '@/types/aov';

export const frameTypes: FrameType[] = [
  // Khung cũ - Có viền
  { id: 'old-border-5vs5', name: '5vs5 2024', category: 'old', hasBorder: true, path: '/old/border/5vs5_2024' },
  { id: 'old-border-christmas', name: 'Christmas', category: 'old', hasBorder: true, path: '/old/border/Christmas' },
  { id: 'old-border-demon-slayer', name: 'Demon Slayer', category: 'old', hasBorder: true, path: '/old/border/Demon Slayer' },
  { id: 'old-border-aic', name: '<PERSON><PERSON>', category: 'old', hasBorder: true, path: '/old/border/aic' },
  { id: 'old-border-aog', name: 'AO<PERSON>', category: 'old', hasBorder: true, path: '/old/border/aog' },
  { id: 'old-border-bleach', name: 'Bleach', category: 'old', hasBorder: true, path: '/old/border/bleach' },
  { id: 'old-border-mystic', name: 'Mystic', category: 'old', hasBorder: true, path: '/old/border/mystic' },
  { id: 'old-border-wave', name: 'WaVe', category: 'old', hasBorder: true, path: '/old/border/wave' },

  // Khung cũ - Không viền
  { id: 'old-no-border-5vs5', name: '5vs5 2024', category: 'old', hasBorder: false, path: '/old/no-border/5vs5_2024' },
  { id: 'old-no-border-christmas', name: 'Christmas', category: 'old', hasBorder: false, path: '/old/no-border/Christmas' },
  { id: 'old-no-border-demon-slayer', name: 'Demon Slayer', category: 'old', hasBorder: false, path: '/old/no-border/Demon Slayer' },
  { id: 'old-no-border-aic', name: 'AIC', category: 'old', hasBorder: false, path: '/old/no-border/aic' },
  { id: 'old-no-border-aog', name: 'AOG', category: 'old', hasBorder: false, path: '/old/no-border/aog' },
  { id: 'old-no-border-bleach', name: 'Bleach', category: 'old', hasBorder: false, path: '/old/no-border/bleach' },
  { id: 'old-no-border-mystic', name: 'Mystic', category: 'old', hasBorder: false, path: '/old/no-border/mystic' },
  { id: 'old-no-border-wave', name: 'WaVe', category: 'old', hasBorder: false, path: '/old/no-border/wave' },

  // Khung mới
  { id: 'new-border-all', name: 'All', category: 'new', hasBorder: true, path: '/new/border/all' },
  { id: 'new-no-border-all', name: 'All', category: 'new', hasBorder: false, path: '/new/no-border/all' },
];

export const companions: Companion[] = [
  { id: 'none', name: 'Không có tri kỷ', image: '' },
  { id: 'companion-1', name: 'Tri kỷ 1', image: '/assets/companions/companion-1.png' },
  { id: 'companion-2', name: 'Tri kỷ 2', image: '/assets/companions/companion-2.png' },
];

export const masteryLevels: MasteryLevel[] = [
  { id: 'none', name: 'Không có huy hiệu', image: '', color: '' },
  { id: 'd', name: 'D', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjOEI0NTEzIi8+CjxyZWN0IHg9IjEiIHk9IjEiIHdpZHRoPSI2MiIgaGVpZ2h0PSI2MiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkQ8L3RleHQ+Cjwvc3ZnPgo=', color: '#8B4513' },
  { id: 'c', name: 'C', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjQ0Q3RjMyIi8+CjxyZWN0IHg9IjEiIHk9IjEiIHdpZHRoPSI2MiIgaGVpZ2h0PSI2MiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkM8L3RleHQ+Cjwvc3ZnPgo=', color: '#CD7F32' },
  { id: 'b', name: 'B', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjQzBDMEMwIi8+CjxyZWN0IHg9IjEiIHk9IjEiIHdpZHRoPSI2MiIgaGVpZ2h0PSI2MiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iYmxhY2siIHRleHQtYW5jaG9yPSJtaWRkbGUiPkI8L3RleHQ+Cjwvc3ZnPgo=', color: '#C0C0C0' },
  { id: 'a', name: 'A', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRkZENzAwIi8+CjxyZWN0IHg9IjEiIHk9IjEiIHdpZHRoPSI2MiIgaGVpZ2h0PSI2MiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iYmxhY2siIHRleHQtYW5jaG9yPSJtaWRkbGUiPkE8L3RleHQ+Cjwvc3ZnPgo=', color: '#FFD700' },
  { id: 's', name: 'S', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRkY2QjZCIi8+CjxyZWN0IHg9IjEiIHk9IjEiIHdpZHRoPSI2MiIgaGVpZ2h0PSI2MiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPlM8L3RleHQ+Cjwvc3ZnPgo=', color: '#FF6B6B' },
  { id: 'green', name: 'Xanh lá', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjNEVDREMwIi8+CjxyZWN0IHg9IjEiIHk9IjEiIHdpZHRoPSI2MiIgaGVpZ2h0PSI2MiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE4IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkc8L3RleHQ+Cjwvc3ZnPgo=', color: '#4ECDC4' },
  { id: 'blue', name: 'Xanh dương', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjNDVCN0QxIi8+CjxyZWN0IHg9IjEiIHk9IjEiIHdpZHRoPSI2MiIgaGVpZ2h0PSI2MiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE4IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkI8L3RleHQ+Cjwvc3ZnPgo=', color: '#45B7D1' },
  { id: 'purple', name: 'Tím', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjOUI1OUI2Ii8+CjxyZWN0IHg9IjEiIHk9IjEiIHdpZHRoPSI2MiIgaGVpZ2h0PSI2MiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE4IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPlA8L3RleHQ+Cjwvc3ZnPgo=', color: '#9B59B6' },
  { id: 'yellow', name: 'Vàng', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjM5QzEyIi8+CjxyZWN0IHg9IjEiIHk9IjEiIHdpZHRoPSI2MiIgaGVpZ2h0PSI2MiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE4IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iYmxhY2siIHRleHQtYW5jaG9yPSJtaWRkbGUiPlk8L3RleHQ+Cjwvc3ZnPgo=', color: '#F39C12' },
];

export const spells: Spell[] = [
  { id: 'none', name: 'Không có phép bổ trợ', image: '' },
  { id: 'bocpha', name: 'Bộc phá', image: '/assets/elements/bocpha.png' },
  { id: 'capcuu', name: 'Cấp cứu', image: '/assets/elements/capcuu.png' },
  { id: 'gamthet', name: 'Gầm thét', image: '/assets/elements/gamthet.png' },
  { id: 'ngatngu', name: 'Ngất ngư', image: '/assets/elements/ngatngu.png' },
  { id: 'suynhuoc', name: 'Suy nhược', image: '/assets/elements/suynhuoc.png' },
  { id: 'thanhtay', name: 'Thanh tẩy', image: '/assets/elements/thanhtay.png' },
  { id: 'tocbien', name: 'Tốc biến', image: '/assets/elements/tocbien.png' },
  { id: 'tochanh', name: 'Tốc hành', image: '/assets/elements/tochanh.png' },
  { id: 'trungtri', name: 'Trừng trị', image: '/assets/elements/trungtri.png' },
];

export const ranks: Rank[] = [
  { id: 'none', name: 'Không có hạng', image: '', tier: '' },
  { id: 'bronze', name: 'Đồng', image: '/assets/ranks/bronze.png', tier: 'Bronze' },
  { id: 'silver', name: 'Bạc', image: '/assets/ranks/silver.png', tier: 'Silver' },
  { id: 'gold', name: 'Vàng', image: '/assets/ranks/gold.png', tier: 'Gold' },
  { id: 'platinum', name: 'Bạch Kim', image: '/assets/ranks/platinum.png', tier: 'Platinum' },
  { id: 'diamond', name: 'Kim Cương', image: '/assets/ranks/diamond.png', tier: 'Diamond' },
  { id: 'master', name: 'Cao Thủ', image: '/assets/ranks/master.png', tier: 'Master' },
  { id: 'grandmaster', name: 'Đại Cao Thủ', image: '/assets/ranks/grandmaster.png', tier: 'Grandmaster' },
  { id: 'challenger', name: 'Thách Đấu', image: '/assets/ranks/challenger.png', tier: 'Challenger' },
];
