"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dom-serializer";
exports.ids = ["vendor-chunks/dom-serializer"];
exports.modules = {

/***/ "(rsc)/./node_modules/dom-serializer/lib/esm/foreignNames.js":
/*!*************************************************************!*\
  !*** ./node_modules/dom-serializer/lib/esm/foreignNames.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attributeNames: () => (/* binding */ attributeNames),\n/* harmony export */   elementNames: () => (/* binding */ elementNames)\n/* harmony export */ });\nconst elementNames = new Map([\n    \"altGlyph\",\n    \"altGlyphDef\",\n    \"altGlyphItem\",\n    \"animateColor\",\n    \"animateMotion\",\n    \"animateTransform\",\n    \"clipPath\",\n    \"feBlend\",\n    \"feColorMatrix\",\n    \"feComponentTransfer\",\n    \"feComposite\",\n    \"feConvolveMatrix\",\n    \"feDiffuseLighting\",\n    \"feDisplacementMap\",\n    \"feDistantLight\",\n    \"feDropShadow\",\n    \"feFlood\",\n    \"feFuncA\",\n    \"feFuncB\",\n    \"feFuncG\",\n    \"feFuncR\",\n    \"feGaussianBlur\",\n    \"feImage\",\n    \"feMerge\",\n    \"feMergeNode\",\n    \"feMorphology\",\n    \"feOffset\",\n    \"fePointLight\",\n    \"feSpecularLighting\",\n    \"feSpotLight\",\n    \"feTile\",\n    \"feTurbulence\",\n    \"foreignObject\",\n    \"glyphRef\",\n    \"linearGradient\",\n    \"radialGradient\",\n    \"textPath\",\n].map((val) => [val.toLowerCase(), val]));\nconst attributeNames = new Map([\n    \"definitionURL\",\n    \"attributeName\",\n    \"attributeType\",\n    \"baseFrequency\",\n    \"baseProfile\",\n    \"calcMode\",\n    \"clipPathUnits\",\n    \"diffuseConstant\",\n    \"edgeMode\",\n    \"filterUnits\",\n    \"glyphRef\",\n    \"gradientTransform\",\n    \"gradientUnits\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keyPoints\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"lengthAdjust\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerUnits\",\n    \"markerWidth\",\n    \"maskContentUnits\",\n    \"maskUnits\",\n    \"numOctaves\",\n    \"pathLength\",\n    \"patternContentUnits\",\n    \"patternTransform\",\n    \"patternUnits\",\n    \"pointsAtX\",\n    \"pointsAtY\",\n    \"pointsAtZ\",\n    \"preserveAlpha\",\n    \"preserveAspectRatio\",\n    \"primitiveUnits\",\n    \"refX\",\n    \"refY\",\n    \"repeatCount\",\n    \"repeatDur\",\n    \"requiredExtensions\",\n    \"requiredFeatures\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"spreadMethod\",\n    \"startOffset\",\n    \"stdDeviation\",\n    \"stitchTiles\",\n    \"surfaceScale\",\n    \"systemLanguage\",\n    \"tableValues\",\n    \"targetX\",\n    \"targetY\",\n    \"textLength\",\n    \"viewBox\",\n    \"viewTarget\",\n    \"xChannelSelector\",\n    \"yChannelSelector\",\n    \"zoomAndPan\",\n].map((val) => [val.toLowerCase(), val]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/dom-serializer/lib/esm/foreignNames.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/dom-serializer/lib/esm/index.js":
/*!******************************************************!*\
  !*** ./node_modules/dom-serializer/lib/esm/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   render: () => (/* binding */ render)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var entities__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! entities */ \"(rsc)/./node_modules/entities/lib/esm/index.js\");\n/* harmony import */ var _foreignNames_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./foreignNames.js */ \"(rsc)/./node_modules/dom-serializer/lib/esm/foreignNames.js\");\n/*\n * Module dependencies\n */\n\n\n/**\n * Mixed-case SVG and MathML tags & attributes\n * recognized by the HTML parser.\n *\n * @see https://html.spec.whatwg.org/multipage/parsing.html#parsing-main-inforeign\n */\n\nconst unencodedElements = new Set([\n    \"style\",\n    \"script\",\n    \"xmp\",\n    \"iframe\",\n    \"noembed\",\n    \"noframes\",\n    \"plaintext\",\n    \"noscript\",\n]);\nfunction replaceQuotes(value) {\n    return value.replace(/\"/g, \"&quot;\");\n}\n/**\n * Format attributes\n */\nfunction formatAttributes(attributes, opts) {\n    var _a;\n    if (!attributes)\n        return;\n    const encode = ((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) === false\n        ? replaceQuotes\n        : opts.xmlMode || opts.encodeEntities !== \"utf8\"\n            ? entities__WEBPACK_IMPORTED_MODULE_1__.encodeXML\n            : entities__WEBPACK_IMPORTED_MODULE_1__.escapeAttribute;\n    return Object.keys(attributes)\n        .map((key) => {\n        var _a, _b;\n        const value = (_a = attributes[key]) !== null && _a !== void 0 ? _a : \"\";\n        if (opts.xmlMode === \"foreign\") {\n            /* Fix up mixed-case attribute names */\n            key = (_b = _foreignNames_js__WEBPACK_IMPORTED_MODULE_2__.attributeNames.get(key)) !== null && _b !== void 0 ? _b : key;\n        }\n        if (!opts.emptyAttrs && !opts.xmlMode && value === \"\") {\n            return key;\n        }\n        return `${key}=\"${encode(value)}\"`;\n    })\n        .join(\" \");\n}\n/**\n * Self-enclosing tags\n */\nconst singleTag = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\n/**\n * Renders a DOM node or an array of DOM nodes to a string.\n *\n * Can be thought of as the equivalent of the `outerHTML` of the passed node(s).\n *\n * @param node Node to be rendered.\n * @param options Changes serialization behavior\n */\nfunction render(node, options = {}) {\n    const nodes = \"length\" in node ? node : [node];\n    let output = \"\";\n    for (let i = 0; i < nodes.length; i++) {\n        output += renderNode(nodes[i], options);\n    }\n    return output;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (render);\nfunction renderNode(node, options) {\n    switch (node.type) {\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Root:\n            return render(node.children, options);\n        // @ts-expect-error We don't use `Doctype` yet\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Doctype:\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Directive:\n            return renderDirective(node);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Comment:\n            return renderComment(node);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.CDATA:\n            return renderCdata(node);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Script:\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Style:\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Tag:\n            return renderTag(node, options);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Text:\n            return renderText(node, options);\n    }\n}\nconst foreignModeIntegrationPoints = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignObject\",\n    \"desc\",\n    \"title\",\n]);\nconst foreignElements = new Set([\"svg\", \"math\"]);\nfunction renderTag(elem, opts) {\n    var _a;\n    // Handle SVG / MathML in HTML\n    if (opts.xmlMode === \"foreign\") {\n        /* Fix up mixed-case element names */\n        elem.name = (_a = _foreignNames_js__WEBPACK_IMPORTED_MODULE_2__.elementNames.get(elem.name)) !== null && _a !== void 0 ? _a : elem.name;\n        /* Exit foreign mode at integration points */\n        if (elem.parent &&\n            foreignModeIntegrationPoints.has(elem.parent.name)) {\n            opts = { ...opts, xmlMode: false };\n        }\n    }\n    if (!opts.xmlMode && foreignElements.has(elem.name)) {\n        opts = { ...opts, xmlMode: \"foreign\" };\n    }\n    let tag = `<${elem.name}`;\n    const attribs = formatAttributes(elem.attribs, opts);\n    if (attribs) {\n        tag += ` ${attribs}`;\n    }\n    if (elem.children.length === 0 &&\n        (opts.xmlMode\n            ? // In XML mode or foreign mode, and user hasn't explicitly turned off self-closing tags\n                opts.selfClosingTags !== false\n            : // User explicitly asked for self-closing tags, even in HTML mode\n                opts.selfClosingTags && singleTag.has(elem.name))) {\n        if (!opts.xmlMode)\n            tag += \" \";\n        tag += \"/>\";\n    }\n    else {\n        tag += \">\";\n        if (elem.children.length > 0) {\n            tag += render(elem.children, opts);\n        }\n        if (opts.xmlMode || !singleTag.has(elem.name)) {\n            tag += `</${elem.name}>`;\n        }\n    }\n    return tag;\n}\nfunction renderDirective(elem) {\n    return `<${elem.data}>`;\n}\nfunction renderText(elem, opts) {\n    var _a;\n    let data = elem.data || \"\";\n    // If entities weren't decoded, no need to encode them back\n    if (((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) !== false &&\n        !(!opts.xmlMode &&\n            elem.parent &&\n            unencodedElements.has(elem.parent.name))) {\n        data =\n            opts.xmlMode || opts.encodeEntities !== \"utf8\"\n                ? (0,entities__WEBPACK_IMPORTED_MODULE_1__.encodeXML)(data)\n                : (0,entities__WEBPACK_IMPORTED_MODULE_1__.escapeText)(data);\n    }\n    return data;\n}\nfunction renderCdata(elem) {\n    return `<![CDATA[${elem.children[0].data}]]>`;\n}\nfunction renderComment(elem) {\n    return `<!--${elem.data}-->`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/dom-serializer/lib/esm/index.js\n");

/***/ })

};
;