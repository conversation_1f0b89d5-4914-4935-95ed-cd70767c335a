"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/skins/[heroSlug]/route";
exports.ids = ["app/api/skins/[heroSlug]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute&page=%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute&page=%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_Website_AOV_data_main_src_app_api_skins_heroSlug_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/skins/[heroSlug]/route.ts */ \"(rsc)/./src/app/api/skins/[heroSlug]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/skins/[heroSlug]/route\",\n        pathname: \"/api/skins/[heroSlug]\",\n        filename: \"route\",\n        bundlePath: \"app/api/skins/[heroSlug]/route\"\n    },\n    resolvedPagePath: \"E:\\\\Website\\\\AOV\\\\data-main\\\\src\\\\app\\\\api\\\\skins\\\\[heroSlug]\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_Website_AOV_data_main_src_app_api_skins_heroSlug_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/skins/[heroSlug]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute&page=%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/skins/[heroSlug]/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/skins/[heroSlug]/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_data_fetcher__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data-fetcher */ \"(rsc)/./src/lib/data-fetcher.ts\");\n\n\nasync function GET(request, { params }) {\n    try {\n        const { heroSlug } = params;\n        const dataFetcher = _lib_data_fetcher__WEBPACK_IMPORTED_MODULE_1__.AOVDataFetcher.getInstance();\n        const skins = await dataFetcher.fetchHeroSkins(heroSlug);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(skins);\n    } catch (error) {\n        console.error(\"Error in skins API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch skins\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9za2lucy9baGVyb1NsdWddL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyQztBQUNTO0FBRTdDLGVBQWVFLElBQ3BCQyxPQUFnQixFQUNoQixFQUFFQyxNQUFNLEVBQW9DO0lBRTVDLElBQUk7UUFDRixNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHRDtRQUNyQixNQUFNRSxjQUFjTCw2REFBY0EsQ0FBQ00sV0FBVztRQUM5QyxNQUFNQyxRQUFRLE1BQU1GLFlBQVlHLGNBQWMsQ0FBQ0o7UUFFL0MsT0FBT0wscURBQVlBLENBQUNVLElBQUksQ0FBQ0Y7SUFDM0IsRUFBRSxPQUFPRyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyx1QkFBdUJBO1FBQ3JDLE9BQU9YLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO1lBQUVDLE9BQU87UUFBd0IsR0FDakM7WUFBRUUsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hb3YtZnJhbWUtZ2VuZXJhdG9yLy4vc3JjL2FwcC9hcGkvc2tpbnMvW2hlcm9TbHVnXS9yb3V0ZS50cz8xZWI2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IEFPVkRhdGFGZXRjaGVyIH0gZnJvbSAnQC9saWIvZGF0YS1mZXRjaGVyJztcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChcbiAgcmVxdWVzdDogUmVxdWVzdCxcbiAgeyBwYXJhbXMgfTogeyBwYXJhbXM6IHsgaGVyb1NsdWc6IHN0cmluZyB9IH1cbikge1xuICB0cnkge1xuICAgIGNvbnN0IHsgaGVyb1NsdWcgfSA9IHBhcmFtcztcbiAgICBjb25zdCBkYXRhRmV0Y2hlciA9IEFPVkRhdGFGZXRjaGVyLmdldEluc3RhbmNlKCk7XG4gICAgY29uc3Qgc2tpbnMgPSBhd2FpdCBkYXRhRmV0Y2hlci5mZXRjaEhlcm9Ta2lucyhoZXJvU2x1Zyk7XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHNraW5zKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBza2lucyBBUEk6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggc2tpbnMnIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiQU9WRGF0YUZldGNoZXIiLCJHRVQiLCJyZXF1ZXN0IiwicGFyYW1zIiwiaGVyb1NsdWciLCJkYXRhRmV0Y2hlciIsImdldEluc3RhbmNlIiwic2tpbnMiLCJmZXRjaEhlcm9Ta2lucyIsImpzb24iLCJlcnJvciIsImNvbnNvbGUiLCJzdGF0dXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/skins/[heroSlug]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/data-fetcher.ts":
/*!*********************************!*\
  !*** ./src/lib/data-fetcher.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AOVDataFetcher: () => (/* binding */ AOVDataFetcher)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(rsc)/./src/lib/utils.ts\");\n/* harmony import */ var _mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mock-data */ \"(rsc)/./src/lib/mock-data.ts\");\n\n\n\n\nconst BASE_URL = \"https://lienquan.garena.vn\";\nclass AOVDataFetcher {\n    static getInstance() {\n        if (!AOVDataFetcher.instance) {\n            AOVDataFetcher.instance = new AOVDataFetcher();\n        }\n        return AOVDataFetcher.instance;\n    }\n    async fetchHeroes() {\n        if (this.heroesCache) {\n            return this.heroesCache;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`${BASE_URL}/hoc-vien/tuong-skin/`, {\n                timeout: 10000\n            });\n            const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(response.data);\n            const heroes = [];\n            $(\".hero-item, .character-item\").each((_, element)=>{\n                const $el = $(element);\n                const name = $el.find(\".hero-name, .character-name\").text().trim();\n                const image = $el.find(\"img\").attr(\"src\") || \"\";\n                const link = $el.find(\"a\").attr(\"href\") || \"\";\n                if (name && image) {\n                    const slug = link.split(\"/\").pop() || (0,_utils__WEBPACK_IMPORTED_MODULE_1__.slugify)(name);\n                    heroes.push({\n                        id: slug,\n                        name,\n                        slug,\n                        image: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatImageUrl)(image),\n                        category: \"hero\"\n                    });\n                }\n            });\n            this.heroesCache = heroes.length > 0 ? heroes : _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockHeroes;\n            return this.heroesCache;\n        } catch (error) {\n            console.error(\"Error fetching heroes, using mock data:\", error);\n            this.heroesCache = _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockHeroes;\n            return _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockHeroes;\n        }\n    }\n    async fetchHeroSkins(heroSlug) {\n        if (this.skinsCache.has(heroSlug)) {\n            return this.skinsCache.get(heroSlug);\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`${BASE_URL}/hoc-vien/tuong-skin/d/${heroSlug}/`, {\n                timeout: 10000\n            });\n            const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(response.data);\n            const skins = [];\n            $(\".skin-item, .hero-skin-item\").each((index, element)=>{\n                const $el = $(element);\n                const name = $el.find(\".skin-name, h3, h4\").text().trim();\n                const image = $el.find(\"img\").attr(\"src\") || \"\";\n                const label = $el.find(\".skin-label img\").attr(\"src\") || \"\";\n                if (name && image) {\n                    skins.push({\n                        id: `${heroSlug}-${index}`,\n                        name,\n                        heroId: heroSlug,\n                        image: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatImageUrl)(image),\n                        rarity: this.extractRarity(name, label),\n                        label: label ? (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatImageUrl)(label) : undefined\n                    });\n                }\n            });\n            const finalSkins = skins.length > 0 ? skins : _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockSkins[heroSlug] || [];\n            this.skinsCache.set(heroSlug, finalSkins);\n            return finalSkins;\n        } catch (error) {\n            console.error(`Error fetching skins for ${heroSlug}, using mock data:`, error);\n            const fallbackSkins = _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockSkins[heroSlug] || [];\n            this.skinsCache.set(heroSlug, fallbackSkins);\n            return fallbackSkins;\n        }\n    }\n    extractRarity(name, label) {\n        const lowerName = name.toLowerCase();\n        const lowerLabel = label.toLowerCase();\n        if (lowerLabel.includes(\"sss\") || lowerName.includes(\"sss\")) return \"SSS\";\n        if (lowerLabel.includes(\"ss\") || lowerName.includes(\"ss\")) return \"SS\";\n        if (lowerLabel.includes(\"s+\") || lowerName.includes(\"s+\")) return \"S+\";\n        if (lowerLabel.includes(\"s\") || lowerName.includes(\"s\")) return \"S\";\n        if (lowerLabel.includes(\"a\") || lowerName.includes(\"a\")) return \"A\";\n        return \"Normal\";\n    }\n    clearCache() {\n        this.heroesCache = null;\n        this.skinsCache.clear();\n    }\n    constructor(){\n        this.heroesCache = null;\n        this.skinsCache = new Map();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGEtZmV0Y2hlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEwQjtBQUNTO0FBRWU7QUFDRTtBQUVwRCxNQUFNTSxXQUFXO0FBRVYsTUFBTUM7SUFLWCxPQUFPQyxjQUE4QjtRQUNuQyxJQUFJLENBQUNELGVBQWVFLFFBQVEsRUFBRTtZQUM1QkYsZUFBZUUsUUFBUSxHQUFHLElBQUlGO1FBQ2hDO1FBQ0EsT0FBT0EsZUFBZUUsUUFBUTtJQUNoQztJQUVBLE1BQU1DLGNBQStCO1FBQ25DLElBQUksSUFBSSxDQUFDQyxXQUFXLEVBQUU7WUFDcEIsT0FBTyxJQUFJLENBQUNBLFdBQVc7UUFDekI7UUFFQSxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNWiw2Q0FBS0EsQ0FBQ2EsR0FBRyxDQUFDLENBQUMsRUFBRVAsU0FBUyxxQkFBcUIsQ0FBQyxFQUFFO2dCQUNuRVEsU0FBUztZQUNYO1lBQ0EsTUFBTUMsSUFBSWQseUNBQVksQ0FBQ1csU0FBU0ssSUFBSTtZQUNwQyxNQUFNQyxTQUFpQixFQUFFO1lBRXpCSCxFQUFFLCtCQUErQkksSUFBSSxDQUFDLENBQUNDLEdBQUdDO2dCQUN4QyxNQUFNQyxNQUFNUCxFQUFFTTtnQkFDZCxNQUFNRSxPQUFPRCxJQUFJRSxJQUFJLENBQUMsK0JBQStCQyxJQUFJLEdBQUdDLElBQUk7Z0JBQ2hFLE1BQU1DLFFBQVFMLElBQUlFLElBQUksQ0FBQyxPQUFPSSxJQUFJLENBQUMsVUFBVTtnQkFDN0MsTUFBTUMsT0FBT1AsSUFBSUUsSUFBSSxDQUFDLEtBQUtJLElBQUksQ0FBQyxXQUFXO2dCQUUzQyxJQUFJTCxRQUFRSSxPQUFPO29CQUNqQixNQUFNRyxPQUFPRCxLQUFLRSxLQUFLLENBQUMsS0FBS0MsR0FBRyxNQUFNOUIsK0NBQU9BLENBQUNxQjtvQkFDOUNMLE9BQU9lLElBQUksQ0FBQzt3QkFDVkMsSUFBSUo7d0JBQ0pQO3dCQUNBTzt3QkFDQUgsT0FBT3hCLHNEQUFjQSxDQUFDd0I7d0JBQ3RCUSxVQUFVO29CQUNaO2dCQUNGO1lBQ0Y7WUFFQSxJQUFJLENBQUN4QixXQUFXLEdBQUdPLE9BQU9rQixNQUFNLEdBQUcsSUFBSWxCLFNBQVNkLGtEQUFVQTtZQUMxRCxPQUFPLElBQUksQ0FBQ08sV0FBVztRQUN6QixFQUFFLE9BQU8wQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywyQ0FBMkNBO1lBQ3pELElBQUksQ0FBQzFCLFdBQVcsR0FBR1Asa0RBQVVBO1lBQzdCLE9BQU9BLGtEQUFVQTtRQUNuQjtJQUNGO0lBRUEsTUFBTW1DLGVBQWVDLFFBQWdCLEVBQW1CO1FBQ3RELElBQUksSUFBSSxDQUFDQyxVQUFVLENBQUNDLEdBQUcsQ0FBQ0YsV0FBVztZQUNqQyxPQUFPLElBQUksQ0FBQ0MsVUFBVSxDQUFDNUIsR0FBRyxDQUFDMkI7UUFDN0I7UUFFQSxJQUFJO1lBQ0YsTUFBTTVCLFdBQVcsTUFBTVosNkNBQUtBLENBQUNhLEdBQUcsQ0FBQyxDQUFDLEVBQUVQLFNBQVMsdUJBQXVCLEVBQUVrQyxTQUFTLENBQUMsQ0FBQyxFQUFFO2dCQUNqRjFCLFNBQVM7WUFDWDtZQUNBLE1BQU1DLElBQUlkLHlDQUFZLENBQUNXLFNBQVNLLElBQUk7WUFDcEMsTUFBTTBCLFFBQWdCLEVBQUU7WUFFeEI1QixFQUFFLCtCQUErQkksSUFBSSxDQUFDLENBQUN5QixPQUFPdkI7Z0JBQzVDLE1BQU1DLE1BQU1QLEVBQUVNO2dCQUNkLE1BQU1FLE9BQU9ELElBQUlFLElBQUksQ0FBQyxzQkFBc0JDLElBQUksR0FBR0MsSUFBSTtnQkFDdkQsTUFBTUMsUUFBUUwsSUFBSUUsSUFBSSxDQUFDLE9BQU9JLElBQUksQ0FBQyxVQUFVO2dCQUM3QyxNQUFNaUIsUUFBUXZCLElBQUlFLElBQUksQ0FBQyxtQkFBbUJJLElBQUksQ0FBQyxVQUFVO2dCQUV6RCxJQUFJTCxRQUFRSSxPQUFPO29CQUNqQmdCLE1BQU1WLElBQUksQ0FBQzt3QkFDVEMsSUFBSSxDQUFDLEVBQUVNLFNBQVMsQ0FBQyxFQUFFSSxNQUFNLENBQUM7d0JBQzFCckI7d0JBQ0F1QixRQUFRTjt3QkFDUmIsT0FBT3hCLHNEQUFjQSxDQUFDd0I7d0JBQ3RCb0IsUUFBUSxJQUFJLENBQUNDLGFBQWEsQ0FBQ3pCLE1BQU1zQjt3QkFDakNBLE9BQU9BLFFBQVExQyxzREFBY0EsQ0FBQzBDLFNBQVNJO29CQUN6QztnQkFDRjtZQUNGO1lBRUEsTUFBTUMsYUFBYVAsTUFBTVAsTUFBTSxHQUFHLElBQUlPLFFBQVN0QyxpREFBUyxDQUFDbUMsU0FBUyxJQUFJLEVBQUU7WUFDeEUsSUFBSSxDQUFDQyxVQUFVLENBQUNVLEdBQUcsQ0FBQ1gsVUFBVVU7WUFDOUIsT0FBT0E7UUFDVCxFQUFFLE9BQU9iLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLENBQUMseUJBQXlCLEVBQUVHLFNBQVMsa0JBQWtCLENBQUMsRUFBRUg7WUFDeEUsTUFBTWUsZ0JBQWdCL0MsaURBQVMsQ0FBQ21DLFNBQVMsSUFBSSxFQUFFO1lBQy9DLElBQUksQ0FBQ0MsVUFBVSxDQUFDVSxHQUFHLENBQUNYLFVBQVVZO1lBQzlCLE9BQU9BO1FBQ1Q7SUFDRjtJQUVRSixjQUFjekIsSUFBWSxFQUFFc0IsS0FBYSxFQUFVO1FBQ3pELE1BQU1RLFlBQVk5QixLQUFLK0IsV0FBVztRQUNsQyxNQUFNQyxhQUFhVixNQUFNUyxXQUFXO1FBRXBDLElBQUlDLFdBQVdDLFFBQVEsQ0FBQyxVQUFVSCxVQUFVRyxRQUFRLENBQUMsUUFBUSxPQUFPO1FBQ3BFLElBQUlELFdBQVdDLFFBQVEsQ0FBQyxTQUFTSCxVQUFVRyxRQUFRLENBQUMsT0FBTyxPQUFPO1FBQ2xFLElBQUlELFdBQVdDLFFBQVEsQ0FBQyxTQUFTSCxVQUFVRyxRQUFRLENBQUMsT0FBTyxPQUFPO1FBQ2xFLElBQUlELFdBQVdDLFFBQVEsQ0FBQyxRQUFRSCxVQUFVRyxRQUFRLENBQUMsTUFBTSxPQUFPO1FBQ2hFLElBQUlELFdBQVdDLFFBQVEsQ0FBQyxRQUFRSCxVQUFVRyxRQUFRLENBQUMsTUFBTSxPQUFPO1FBRWhFLE9BQU87SUFDVDtJQUVBQyxhQUFtQjtRQUNqQixJQUFJLENBQUM5QyxXQUFXLEdBQUc7UUFDbkIsSUFBSSxDQUFDOEIsVUFBVSxDQUFDaUIsS0FBSztJQUN2Qjs7YUExR1EvQyxjQUE2QjthQUM3QjhCLGFBQWtDLElBQUlrQjs7QUEwR2hEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW92LWZyYW1lLWdlbmVyYXRvci8uL3NyYy9saWIvZGF0YS1mZXRjaGVyLnRzP2E3YTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJztcbmltcG9ydCAqIGFzIGNoZWVyaW8gZnJvbSAnY2hlZXJpbyc7XG5pbXBvcnQgeyBIZXJvLCBTa2luIH0gZnJvbSAnQC90eXBlcy9hb3YnO1xuaW1wb3J0IHsgc2x1Z2lmeSwgZm9ybWF0SW1hZ2VVcmwgfSBmcm9tICcuL3V0aWxzJztcbmltcG9ydCB7IG1vY2tIZXJvZXMsIG1vY2tTa2lucyB9IGZyb20gJy4vbW9jay1kYXRhJztcblxuY29uc3QgQkFTRV9VUkwgPSAnaHR0cHM6Ly9saWVucXVhbi5nYXJlbmEudm4nO1xuXG5leHBvcnQgY2xhc3MgQU9WRGF0YUZldGNoZXIge1xuICBwcml2YXRlIHN0YXRpYyBpbnN0YW5jZTogQU9WRGF0YUZldGNoZXI7XG4gIHByaXZhdGUgaGVyb2VzQ2FjaGU6IEhlcm9bXSB8IG51bGwgPSBudWxsO1xuICBwcml2YXRlIHNraW5zQ2FjaGU6IE1hcDxzdHJpbmcsIFNraW5bXT4gPSBuZXcgTWFwKCk7XG5cbiAgc3RhdGljIGdldEluc3RhbmNlKCk6IEFPVkRhdGFGZXRjaGVyIHtcbiAgICBpZiAoIUFPVkRhdGFGZXRjaGVyLmluc3RhbmNlKSB7XG4gICAgICBBT1ZEYXRhRmV0Y2hlci5pbnN0YW5jZSA9IG5ldyBBT1ZEYXRhRmV0Y2hlcigpO1xuICAgIH1cbiAgICByZXR1cm4gQU9WRGF0YUZldGNoZXIuaW5zdGFuY2U7XG4gIH1cblxuICBhc3luYyBmZXRjaEhlcm9lcygpOiBQcm9taXNlPEhlcm9bXT4ge1xuICAgIGlmICh0aGlzLmhlcm9lc0NhY2hlKSB7XG4gICAgICByZXR1cm4gdGhpcy5oZXJvZXNDYWNoZTtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoYCR7QkFTRV9VUkx9L2hvYy12aWVuL3R1b25nLXNraW4vYCwge1xuICAgICAgICB0aW1lb3V0OiAxMDAwMFxuICAgICAgfSk7XG4gICAgICBjb25zdCAkID0gY2hlZXJpby5sb2FkKHJlc3BvbnNlLmRhdGEpO1xuICAgICAgY29uc3QgaGVyb2VzOiBIZXJvW10gPSBbXTtcblxuICAgICAgJCgnLmhlcm8taXRlbSwgLmNoYXJhY3Rlci1pdGVtJykuZWFjaCgoXywgZWxlbWVudCkgPT4ge1xuICAgICAgICBjb25zdCAkZWwgPSAkKGVsZW1lbnQpO1xuICAgICAgICBjb25zdCBuYW1lID0gJGVsLmZpbmQoJy5oZXJvLW5hbWUsIC5jaGFyYWN0ZXItbmFtZScpLnRleHQoKS50cmltKCk7XG4gICAgICAgIGNvbnN0IGltYWdlID0gJGVsLmZpbmQoJ2ltZycpLmF0dHIoJ3NyYycpIHx8ICcnO1xuICAgICAgICBjb25zdCBsaW5rID0gJGVsLmZpbmQoJ2EnKS5hdHRyKCdocmVmJykgfHwgJyc7XG5cbiAgICAgICAgaWYgKG5hbWUgJiYgaW1hZ2UpIHtcbiAgICAgICAgICBjb25zdCBzbHVnID0gbGluay5zcGxpdCgnLycpLnBvcCgpIHx8IHNsdWdpZnkobmFtZSk7XG4gICAgICAgICAgaGVyb2VzLnB1c2goe1xuICAgICAgICAgICAgaWQ6IHNsdWcsXG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgc2x1ZyxcbiAgICAgICAgICAgIGltYWdlOiBmb3JtYXRJbWFnZVVybChpbWFnZSksXG4gICAgICAgICAgICBjYXRlZ29yeTogJ2hlcm8nXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICB0aGlzLmhlcm9lc0NhY2hlID0gaGVyb2VzLmxlbmd0aCA+IDAgPyBoZXJvZXMgOiBtb2NrSGVyb2VzO1xuICAgICAgcmV0dXJuIHRoaXMuaGVyb2VzQ2FjaGU7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGhlcm9lcywgdXNpbmcgbW9jayBkYXRhOicsIGVycm9yKTtcbiAgICAgIHRoaXMuaGVyb2VzQ2FjaGUgPSBtb2NrSGVyb2VzO1xuICAgICAgcmV0dXJuIG1vY2tIZXJvZXM7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZmV0Y2hIZXJvU2tpbnMoaGVyb1NsdWc6IHN0cmluZyk6IFByb21pc2U8U2tpbltdPiB7XG4gICAgaWYgKHRoaXMuc2tpbnNDYWNoZS5oYXMoaGVyb1NsdWcpKSB7XG4gICAgICByZXR1cm4gdGhpcy5za2luc0NhY2hlLmdldChoZXJvU2x1ZykhO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChgJHtCQVNFX1VSTH0vaG9jLXZpZW4vdHVvbmctc2tpbi9kLyR7aGVyb1NsdWd9L2AsIHtcbiAgICAgICAgdGltZW91dDogMTAwMDBcbiAgICAgIH0pO1xuICAgICAgY29uc3QgJCA9IGNoZWVyaW8ubG9hZChyZXNwb25zZS5kYXRhKTtcbiAgICAgIGNvbnN0IHNraW5zOiBTa2luW10gPSBbXTtcblxuICAgICAgJCgnLnNraW4taXRlbSwgLmhlcm8tc2tpbi1pdGVtJykuZWFjaCgoaW5kZXgsIGVsZW1lbnQpID0+IHtcbiAgICAgICAgY29uc3QgJGVsID0gJChlbGVtZW50KTtcbiAgICAgICAgY29uc3QgbmFtZSA9ICRlbC5maW5kKCcuc2tpbi1uYW1lLCBoMywgaDQnKS50ZXh0KCkudHJpbSgpO1xuICAgICAgICBjb25zdCBpbWFnZSA9ICRlbC5maW5kKCdpbWcnKS5hdHRyKCdzcmMnKSB8fCAnJztcbiAgICAgICAgY29uc3QgbGFiZWwgPSAkZWwuZmluZCgnLnNraW4tbGFiZWwgaW1nJykuYXR0cignc3JjJykgfHwgJyc7XG5cbiAgICAgICAgaWYgKG5hbWUgJiYgaW1hZ2UpIHtcbiAgICAgICAgICBza2lucy5wdXNoKHtcbiAgICAgICAgICAgIGlkOiBgJHtoZXJvU2x1Z30tJHtpbmRleH1gLFxuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIGhlcm9JZDogaGVyb1NsdWcsXG4gICAgICAgICAgICBpbWFnZTogZm9ybWF0SW1hZ2VVcmwoaW1hZ2UpLFxuICAgICAgICAgICAgcmFyaXR5OiB0aGlzLmV4dHJhY3RSYXJpdHkobmFtZSwgbGFiZWwpLFxuICAgICAgICAgICAgbGFiZWw6IGxhYmVsID8gZm9ybWF0SW1hZ2VVcmwobGFiZWwpIDogdW5kZWZpbmVkXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBmaW5hbFNraW5zID0gc2tpbnMubGVuZ3RoID4gMCA/IHNraW5zIDogKG1vY2tTa2luc1toZXJvU2x1Z10gfHwgW10pO1xuICAgICAgdGhpcy5za2luc0NhY2hlLnNldChoZXJvU2x1ZywgZmluYWxTa2lucyk7XG4gICAgICByZXR1cm4gZmluYWxTa2lucztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihgRXJyb3IgZmV0Y2hpbmcgc2tpbnMgZm9yICR7aGVyb1NsdWd9LCB1c2luZyBtb2NrIGRhdGE6YCwgZXJyb3IpO1xuICAgICAgY29uc3QgZmFsbGJhY2tTa2lucyA9IG1vY2tTa2luc1toZXJvU2x1Z10gfHwgW107XG4gICAgICB0aGlzLnNraW5zQ2FjaGUuc2V0KGhlcm9TbHVnLCBmYWxsYmFja1NraW5zKTtcbiAgICAgIHJldHVybiBmYWxsYmFja1NraW5zO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgZXh0cmFjdFJhcml0eShuYW1lOiBzdHJpbmcsIGxhYmVsOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIGNvbnN0IGxvd2VyTmFtZSA9IG5hbWUudG9Mb3dlckNhc2UoKTtcbiAgICBjb25zdCBsb3dlckxhYmVsID0gbGFiZWwudG9Mb3dlckNhc2UoKTtcblxuICAgIGlmIChsb3dlckxhYmVsLmluY2x1ZGVzKCdzc3MnKSB8fCBsb3dlck5hbWUuaW5jbHVkZXMoJ3NzcycpKSByZXR1cm4gJ1NTUyc7XG4gICAgaWYgKGxvd2VyTGFiZWwuaW5jbHVkZXMoJ3NzJykgfHwgbG93ZXJOYW1lLmluY2x1ZGVzKCdzcycpKSByZXR1cm4gJ1NTJztcbiAgICBpZiAobG93ZXJMYWJlbC5pbmNsdWRlcygncysnKSB8fCBsb3dlck5hbWUuaW5jbHVkZXMoJ3MrJykpIHJldHVybiAnUysnO1xuICAgIGlmIChsb3dlckxhYmVsLmluY2x1ZGVzKCdzJykgfHwgbG93ZXJOYW1lLmluY2x1ZGVzKCdzJykpIHJldHVybiAnUyc7XG4gICAgaWYgKGxvd2VyTGFiZWwuaW5jbHVkZXMoJ2EnKSB8fCBsb3dlck5hbWUuaW5jbHVkZXMoJ2EnKSkgcmV0dXJuICdBJztcblxuICAgIHJldHVybiAnTm9ybWFsJztcbiAgfVxuXG4gIGNsZWFyQ2FjaGUoKTogdm9pZCB7XG4gICAgdGhpcy5oZXJvZXNDYWNoZSA9IG51bGw7XG4gICAgdGhpcy5za2luc0NhY2hlLmNsZWFyKCk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJheGlvcyIsImNoZWVyaW8iLCJzbHVnaWZ5IiwiZm9ybWF0SW1hZ2VVcmwiLCJtb2NrSGVyb2VzIiwibW9ja1NraW5zIiwiQkFTRV9VUkwiLCJBT1ZEYXRhRmV0Y2hlciIsImdldEluc3RhbmNlIiwiaW5zdGFuY2UiLCJmZXRjaEhlcm9lcyIsImhlcm9lc0NhY2hlIiwicmVzcG9uc2UiLCJnZXQiLCJ0aW1lb3V0IiwiJCIsImxvYWQiLCJkYXRhIiwiaGVyb2VzIiwiZWFjaCIsIl8iLCJlbGVtZW50IiwiJGVsIiwibmFtZSIsImZpbmQiLCJ0ZXh0IiwidHJpbSIsImltYWdlIiwiYXR0ciIsImxpbmsiLCJzbHVnIiwic3BsaXQiLCJwb3AiLCJwdXNoIiwiaWQiLCJjYXRlZ29yeSIsImxlbmd0aCIsImVycm9yIiwiY29uc29sZSIsImZldGNoSGVyb1NraW5zIiwiaGVyb1NsdWciLCJza2luc0NhY2hlIiwiaGFzIiwic2tpbnMiLCJpbmRleCIsImxhYmVsIiwiaGVyb0lkIiwicmFyaXR5IiwiZXh0cmFjdFJhcml0eSIsInVuZGVmaW5lZCIsImZpbmFsU2tpbnMiLCJzZXQiLCJmYWxsYmFja1NraW5zIiwibG93ZXJOYW1lIiwidG9Mb3dlckNhc2UiLCJsb3dlckxhYmVsIiwiaW5jbHVkZXMiLCJjbGVhckNhY2hlIiwiY2xlYXIiLCJNYXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/data-fetcher.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mock-data.ts":
/*!******************************!*\
  !*** ./src/lib/mock-data.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockHeroes: () => (/* binding */ mockHeroes),\n/* harmony export */   mockSkins: () => (/* binding */ mockSkins)\n/* harmony export */ });\n// Mock data for testing when API is not available\nconst mockHeroes = [\n    {\n        id: \"alice\",\n        name: \"Alice\",\n        slug: \"alice\",\n        image: \"https://lienquan.garena.vn/wp-content/uploads/2024/05/9b84b7e2b3f71361cc8d0178afb6696e58462fa58e8e01.jpg\",\n        category: \"hero\"\n    },\n    {\n        id: \"airi\",\n        name: \"Airi\",\n        slug: \"airi\",\n        image: \"https://lienquan.garena.vn/wp-content/uploads/2024/05/airi-default.jpg\",\n        category: \"hero\"\n    },\n    {\n        id: \"annette\",\n        name: \"Annette\",\n        slug: \"annette\",\n        image: \"https://lienquan.garena.vn/wp-content/uploads/2024/05/annette-default.jpg\",\n        category: \"hero\"\n    },\n    {\n        id: \"violet\",\n        name: \"Violet\",\n        slug: \"violet\",\n        image: \"https://lienquan.garena.vn/wp-content/uploads/2024/05/violet-default.jpg\",\n        category: \"hero\"\n    },\n    {\n        id: \"hayate\",\n        name: \"Hayate\",\n        slug: \"hayate\",\n        image: \"https://lienquan.garena.vn/wp-content/uploads/2024/05/hayate-default.jpg\",\n        category: \"hero\"\n    }\n];\nconst mockSkins = {\n    alice: [\n        {\n            id: \"alice-default\",\n            name: \"Alice\",\n            heroId: \"alice\",\n            image: \"https://lienquan.garena.vn/wp-content/uploads/2024/05/9b84b7e2b3f71361cc8d0178afb6696e58462fa58e8e01.jpg\",\n            rarity: \"Normal\"\n        },\n        {\n            id: \"alice-astrologer\",\n            name: \"Alice Nh\\xe0 chi\\xeam tinh\",\n            heroId: \"alice\",\n            image: \"https://lienquan.garena.vn/wp-content/uploads/2024/05/4994ed2f082a8bc5a271789f5629e0e058462f5b2a9391.jpg\",\n            rarity: \"A\"\n        },\n        {\n            id: \"alice-snow-bear\",\n            name: \"Alice B\\xe9 Gấu Tuyết\",\n            heroId: \"alice\",\n            image: \"https://lienquan.garena.vn/wp-content/uploads/2024/05/17c8dcdbde640ff14cfef0e8ee13db7c5b7d4a304c6541.jpg\",\n            rarity: \"S+\"\n        },\n        {\n            id: \"alice-wonderland\",\n            name: \"Alice Xứ sở diệu kỳ\",\n            heroId: \"alice\",\n            image: \"https://lienquan.garena.vn/wp-content/uploads/2024/05/cf10b9f467d59666080b94fa188e26f7659518e7edef11.jpg\",\n            rarity: \"SS\"\n        }\n    ],\n    airi: [\n        {\n            id: \"airi-default\",\n            name: \"Airi\",\n            heroId: \"airi\",\n            image: \"https://lienquan.garena.vn/wp-content/uploads/2024/05/airi-default.jpg\",\n            rarity: \"Normal\"\n        },\n        {\n            id: \"airi-beach-party\",\n            name: \"Airi Tiệc B\\xe3i Biển\",\n            heroId: \"airi\",\n            image: \"https://lienquan.garena.vn/wp-content/uploads/2024/05/airi-beach-party.jpg\",\n            rarity: \"S\"\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mock-data.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   downloadImage: () => (/* binding */ downloadImage),\n/* harmony export */   formatImageUrl: () => (/* binding */ formatImageUrl),\n/* harmony export */   slugify: () => (/* binding */ slugify)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction slugify(text) {\n    return text.toLowerCase().normalize(\"NFD\").replace(/[\\u0300-\\u036f]/g, \"\") // Remove diacritics\n    .replace(/[^a-z0-9\\s-]/g, \"\") // Remove special characters\n    .replace(/\\s+/g, \"-\") // Replace spaces with hyphens\n    .replace(/-+/g, \"-\") // Replace multiple hyphens with single\n    .trim();\n}\nfunction formatImageUrl(url) {\n    if (url.startsWith(\"http\")) {\n        return url;\n    }\n    return `https://lienquan.garena.vn${url}`;\n}\nfunction downloadImage(dataUrl, filename) {\n    const link = document.createElement(\"a\");\n    link.download = filename;\n    link.href = dataUrl;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/undici","vendor-chunks/parse5","vendor-chunks/iconv-lite","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/entities","vendor-chunks/cheerio","vendor-chunks/htmlparser2","vendor-chunks/encoding-sniffer","vendor-chunks/css-select","vendor-chunks/domutils","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/css-what","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/domhandler","vendor-chunks/cheerio-select","vendor-chunks/whatwg-mimetype","vendor-chunks/asynckit","vendor-chunks/whatwg-encoding","vendor-chunks/dom-serializer","vendor-chunks/nth-check","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/parse5-parser-stream","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/safer-buffer","vendor-chunks/domelementtype","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/boolbase","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute&page=%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fskins%2F%5BheroSlug%5D%2Froute.ts&appDir=E%3A%5CWebsite%5CAOV%5Cdata-main%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWebsite%5CAOV%5Cdata-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();